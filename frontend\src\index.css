@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-border-primary;
  }
  
  body {
    @apply bg-background-primary text-text-primary;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none;
  }
  
  .btn-primary {
    @apply btn bg-primary-500 text-white hover:bg-primary-600;
  }
  
  .btn-secondary {
    @apply btn bg-background-secondary text-text-primary border border-border-primary hover:bg-background-tertiary;
  }
  
  .btn-danger {
    @apply btn bg-error-500 text-white hover:bg-error-600;
  }
  
  .input {
    @apply flex h-10 w-full rounded-md border border-border-primary bg-background-secondary px-3 py-2 text-sm text-text-primary placeholder:text-text-muted focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }
  
  .card {
    @apply rounded-lg border border-border-primary bg-background-secondary p-6 shadow-sm;
  }
  
  .modal-overlay {
    @apply fixed inset-0 z-50 bg-black/80 backdrop-blur-sm;
  }
  
  .modal-content {
    @apply fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border border-border-primary bg-background-secondary p-6 shadow-lg duration-200 rounded-lg;
  }
}

@layer utilities {
  .text-gradient {
    @apply bg-gradient-to-r from-primary-400 to-primary-600 bg-clip-text text-transparent;
  }
  
  .animate-pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
  
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
}
