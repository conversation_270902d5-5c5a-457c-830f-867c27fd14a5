const fetch = require('node-fetch');

async function testCreditSystem() {
  try {
    // First, login to get a token
    console.log('🔐 Logging in...');
    const loginResponse = await fetch('http://localhost:3001/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'testpass123'
      })
    });

    if (!loginResponse.ok) {
      throw new Error(`Login failed: ${loginResponse.status}`);
    }

    const loginData = await loginResponse.json();
    console.log('Login response:', loginData);
    const token = loginData.token;
    console.log('✅ Login successful');

    // Test 1: Get credit balance
    console.log('\n💰 Testing credit balance...');
    const balanceResponse = await fetch('http://localhost:3001/api/credits/balance', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (!balanceResponse.ok) {
      throw new Error(`Get balance failed: ${balanceResponse.status}`);
    }

    const balanceData = await balanceResponse.json();
    console.log('✅ Credit balance:', balanceData);

    // Test 2: Get operation costs
    console.log('\n💸 Testing operation costs...');
    const costsResponse = await fetch('http://localhost:3001/api/credits/pricing', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (!costsResponse.ok) {
      throw new Error(`Get costs failed: ${costsResponse.status}`);
    }

    const costsData = await costsResponse.json();
    console.log('✅ Operation costs:', costsData);

    // Test 3: Check credit sufficiency
    console.log('\n🔍 Testing credit sufficiency check...');
    const checkResponse = await fetch('http://localhost:3001/api/credits/check/flashcard_generation', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (!checkResponse.ok) {
      throw new Error(`Credit check failed: ${checkResponse.status}`);
    }

    const checkData = await checkResponse.json();
    console.log('✅ Credit sufficiency check:', checkData);

    // Test 4: Get credit history
    console.log('\n📊 Testing credit history...');
    const historyResponse = await fetch('http://localhost:3001/api/credits/history?limit=10', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (!historyResponse.ok) {
      throw new Error(`Get history failed: ${historyResponse.status}`);
    }

    const historyData = await historyResponse.json();
    console.log('✅ Credit history:', historyData);

    // Test 5: Get credit stats
    console.log('\n📈 Testing credit statistics...');
    const statsResponse = await fetch('http://localhost:3001/api/credits/stats?days=30', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (!statsResponse.ok) {
      throw new Error(`Get stats failed: ${statsResponse.status}`);
    }

    const statsData = await statsResponse.json();
    console.log('✅ Credit statistics:', statsData);

    console.log('\n🎉 All credit system tests passed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  }
}

testCreditSystem();
