import { Router } from 'express';
import { authenticateToken } from '../middleware/auth';
import { requireCredits } from '../middleware/creditCheck';
import {
  getCreditBalance,
  getCreditHistory,
  getCreditStats,
  getOperationCosts,
  checkCreditSufficiency
} from '../controllers/creditController';

const router = Router();

// All credit routes require authentication
router.use(authenticateToken);

// Credit information routes
router.get('/balance', getCreditBalance);
router.get('/history', getCreditHistory);
router.get('/stats', getCreditStats);
router.get('/pricing', getOperationCosts);
router.get('/check/:operationType', checkCreditSufficiency);

// Test endpoint to demonstrate credit middleware
router.post('/test-deduction/:operationType', (req, res, next) => {
  const { operationType } = req.params;
  return requireCredits(operationType)(req, res, next);
}, async (req, res) => {
  try {
    const { operationType } = req.params;
    const userId = req.user!.id;

    // Import creditService here to avoid circular dependency
    const { creditService } = await import('../services/creditService');

    // Deduct credits for the operation
    const result = await creditService.deductCredits(
      userId,
      operationType,
      { test: true, endpoint: 'test-deduction' }
    );

    res.json({
      success: true,
      message: 'Credits deducted successfully',
      data: result
    });
  } catch (error) {
    console.error('Test deduction error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to deduct credits'
    });
  }
});

export default router;
