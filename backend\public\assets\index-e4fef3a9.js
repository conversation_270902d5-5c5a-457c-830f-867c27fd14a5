function ch(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const i in r)if(i!=="default"&&!(i in e)){const s=Object.getOwnPropertyDescriptor(r,i);s&&Object.defineProperty(e,i,s.get?s:{enumerable:!0,get:()=>r[i]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const s of i)if(s.type==="childList")for(const o of s.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&r(o)}).observe(document,{childList:!0,subtree:!0});function n(i){const s={};return i.integrity&&(s.integrity=i.integrity),i.referrerPolicy&&(s.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?s.credentials="include":i.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function r(i){if(i.ep)return;i.ep=!0;const s=n(i);fetch(i.href,s)}})();var Le=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function uc(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function dh(e){if(e.__esModule)return e;var t=e.default;if(typeof t=="function"){var n=function r(){return this instanceof r?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};n.prototype=t.prototype}else n={};return Object.defineProperty(n,"__esModule",{value:!0}),Object.keys(e).forEach(function(r){var i=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(n,r,i.get?i:{enumerable:!0,get:function(){return e[r]}})}),n}var cc={exports:{}},ls={},dc={exports:{}},A={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Br=Symbol.for("react.element"),fh=Symbol.for("react.portal"),hh=Symbol.for("react.fragment"),ph=Symbol.for("react.strict_mode"),vh=Symbol.for("react.profiler"),gh=Symbol.for("react.provider"),mh=Symbol.for("react.context"),yh=Symbol.for("react.forward_ref"),wh=Symbol.for("react.suspense"),_h=Symbol.for("react.memo"),Sh=Symbol.for("react.lazy"),Ta=Symbol.iterator;function kh(e){return e===null||typeof e!="object"?null:(e=Ta&&e[Ta]||e["@@iterator"],typeof e=="function"?e:null)}var fc={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},hc=Object.assign,pc={};function Dn(e,t,n){this.props=e,this.context=t,this.refs=pc,this.updater=n||fc}Dn.prototype.isReactComponent={};Dn.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Dn.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function vc(){}vc.prototype=Dn.prototype;function Sl(e,t,n){this.props=e,this.context=t,this.refs=pc,this.updater=n||fc}var kl=Sl.prototype=new vc;kl.constructor=Sl;hc(kl,Dn.prototype);kl.isPureReactComponent=!0;var ja=Array.isArray,gc=Object.prototype.hasOwnProperty,El={current:null},mc={key:!0,ref:!0,__self:!0,__source:!0};function yc(e,t,n){var r,i={},s=null,o=null;if(t!=null)for(r in t.ref!==void 0&&(o=t.ref),t.key!==void 0&&(s=""+t.key),t)gc.call(t,r)&&!mc.hasOwnProperty(r)&&(i[r]=t[r]);var l=arguments.length-2;if(l===1)i.children=n;else if(1<l){for(var a=Array(l),u=0;u<l;u++)a[u]=arguments[u+2];i.children=a}if(e&&e.defaultProps)for(r in l=e.defaultProps,l)i[r]===void 0&&(i[r]=l[r]);return{$$typeof:Br,type:e,key:s,ref:o,props:i,_owner:El.current}}function Eh(e,t){return{$$typeof:Br,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function xl(e){return typeof e=="object"&&e!==null&&e.$$typeof===Br}function xh(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Oa=/\/+/g;function bs(e,t){return typeof e=="object"&&e!==null&&e.key!=null?xh(""+e.key):t.toString(36)}function Si(e,t,n,r,i){var s=typeof e;(s==="undefined"||s==="boolean")&&(e=null);var o=!1;if(e===null)o=!0;else switch(s){case"string":case"number":o=!0;break;case"object":switch(e.$$typeof){case Br:case fh:o=!0}}if(o)return o=e,i=i(o),e=r===""?"."+bs(o,0):r,ja(i)?(n="",e!=null&&(n=e.replace(Oa,"$&/")+"/"),Si(i,t,n,"",function(u){return u})):i!=null&&(xl(i)&&(i=Eh(i,n+(!i.key||o&&o.key===i.key?"":(""+i.key).replace(Oa,"$&/")+"/")+e)),t.push(i)),1;if(o=0,r=r===""?".":r+":",ja(e))for(var l=0;l<e.length;l++){s=e[l];var a=r+bs(s,l);o+=Si(s,t,n,a,i)}else if(a=kh(e),typeof a=="function")for(e=a.call(e),l=0;!(s=e.next()).done;)s=s.value,a=r+bs(s,l++),o+=Si(s,t,n,a,i);else if(s==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return o}function Xr(e,t,n){if(e==null)return e;var r=[],i=0;return Si(e,r,"","",function(s){return t.call(n,s,i++)}),r}function Ch(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var pe={current:null},ki={transition:null},Ph={ReactCurrentDispatcher:pe,ReactCurrentBatchConfig:ki,ReactCurrentOwner:El};function wc(){throw Error("act(...) is not supported in production builds of React.")}A.Children={map:Xr,forEach:function(e,t,n){Xr(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return Xr(e,function(){t++}),t},toArray:function(e){return Xr(e,function(t){return t})||[]},only:function(e){if(!xl(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};A.Component=Dn;A.Fragment=hh;A.Profiler=vh;A.PureComponent=Sl;A.StrictMode=ph;A.Suspense=wh;A.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Ph;A.act=wc;A.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=hc({},e.props),i=e.key,s=e.ref,o=e._owner;if(t!=null){if(t.ref!==void 0&&(s=t.ref,o=El.current),t.key!==void 0&&(i=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(a in t)gc.call(t,a)&&!mc.hasOwnProperty(a)&&(r[a]=t[a]===void 0&&l!==void 0?l[a]:t[a])}var a=arguments.length-2;if(a===1)r.children=n;else if(1<a){l=Array(a);for(var u=0;u<a;u++)l[u]=arguments[u+2];r.children=l}return{$$typeof:Br,type:e.type,key:i,ref:s,props:r,_owner:o}};A.createContext=function(e){return e={$$typeof:mh,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:gh,_context:e},e.Consumer=e};A.createElement=yc;A.createFactory=function(e){var t=yc.bind(null,e);return t.type=e,t};A.createRef=function(){return{current:null}};A.forwardRef=function(e){return{$$typeof:yh,render:e}};A.isValidElement=xl;A.lazy=function(e){return{$$typeof:Sh,_payload:{_status:-1,_result:e},_init:Ch}};A.memo=function(e,t){return{$$typeof:_h,type:e,compare:t===void 0?null:t}};A.startTransition=function(e){var t=ki.transition;ki.transition={};try{e()}finally{ki.transition=t}};A.unstable_act=wc;A.useCallback=function(e,t){return pe.current.useCallback(e,t)};A.useContext=function(e){return pe.current.useContext(e)};A.useDebugValue=function(){};A.useDeferredValue=function(e){return pe.current.useDeferredValue(e)};A.useEffect=function(e,t){return pe.current.useEffect(e,t)};A.useId=function(){return pe.current.useId()};A.useImperativeHandle=function(e,t,n){return pe.current.useImperativeHandle(e,t,n)};A.useInsertionEffect=function(e,t){return pe.current.useInsertionEffect(e,t)};A.useLayoutEffect=function(e,t){return pe.current.useLayoutEffect(e,t)};A.useMemo=function(e,t){return pe.current.useMemo(e,t)};A.useReducer=function(e,t,n){return pe.current.useReducer(e,t,n)};A.useRef=function(e){return pe.current.useRef(e)};A.useState=function(e){return pe.current.useState(e)};A.useSyncExternalStore=function(e,t,n){return pe.current.useSyncExternalStore(e,t,n)};A.useTransition=function(){return pe.current.useTransition()};A.version="18.3.1";dc.exports=A;var x=dc.exports;const Cl=uc(x),Th=ch({__proto__:null,default:Cl},[x]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var jh=x,Oh=Symbol.for("react.element"),Rh=Symbol.for("react.fragment"),Ih=Object.prototype.hasOwnProperty,$h=jh.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Lh={key:!0,ref:!0,__self:!0,__source:!0};function _c(e,t,n){var r,i={},s=null,o=null;n!==void 0&&(s=""+n),t.key!==void 0&&(s=""+t.key),t.ref!==void 0&&(o=t.ref);for(r in t)Ih.call(t,r)&&!Lh.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)i[r]===void 0&&(i[r]=t[r]);return{$$typeof:Oh,type:e,key:s,ref:o,props:i,_owner:$h.current}}ls.Fragment=Rh;ls.jsx=_c;ls.jsxs=_c;cc.exports=ls;var j=cc.exports,mo={},Sc={exports:{}},Te={},kc={exports:{}},Ec={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(T,I){var $=T.length;T.push(I);e:for(;0<$;){var J=$-1>>>1,ee=T[J];if(0<i(ee,I))T[J]=I,T[$]=ee,$=J;else break e}}function n(T){return T.length===0?null:T[0]}function r(T){if(T.length===0)return null;var I=T[0],$=T.pop();if($!==I){T[0]=$;e:for(var J=0,ee=T.length,Gr=ee>>>1;J<Gr;){var Ut=2*(J+1)-1,Ns=T[Ut],Dt=Ut+1,Yr=T[Dt];if(0>i(Ns,$))Dt<ee&&0>i(Yr,Ns)?(T[J]=Yr,T[Dt]=$,J=Dt):(T[J]=Ns,T[Ut]=$,J=Ut);else if(Dt<ee&&0>i(Yr,$))T[J]=Yr,T[Dt]=$,J=Dt;else break e}}return I}function i(T,I){var $=T.sortIndex-I.sortIndex;return $!==0?$:T.id-I.id}if(typeof performance=="object"&&typeof performance.now=="function"){var s=performance;e.unstable_now=function(){return s.now()}}else{var o=Date,l=o.now();e.unstable_now=function(){return o.now()-l}}var a=[],u=[],c=1,d=null,f=3,g=!1,m=!1,w=!1,_=typeof setTimeout=="function"?setTimeout:null,v=typeof clearTimeout=="function"?clearTimeout:null,h=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function p(T){for(var I=n(u);I!==null;){if(I.callback===null)r(u);else if(I.startTime<=T)r(u),I.sortIndex=I.expirationTime,t(a,I);else break;I=n(u)}}function y(T){if(w=!1,p(T),!m)if(n(a)!==null)m=!0,Ls(k);else{var I=n(u);I!==null&&As(y,I.startTime-T)}}function k(T,I){m=!1,w&&(w=!1,v(O),O=-1),g=!0;var $=f;try{for(p(I),d=n(a);d!==null&&(!(d.expirationTime>I)||T&&!be());){var J=d.callback;if(typeof J=="function"){d.callback=null,f=d.priorityLevel;var ee=J(d.expirationTime<=I);I=e.unstable_now(),typeof ee=="function"?d.callback=ee:d===n(a)&&r(a),p(I)}else r(a);d=n(a)}if(d!==null)var Gr=!0;else{var Ut=n(u);Ut!==null&&As(y,Ut.startTime-I),Gr=!1}return Gr}finally{d=null,f=$,g=!1}}var E=!1,C=null,O=-1,K=5,N=-1;function be(){return!(e.unstable_now()-N<K)}function qn(){if(C!==null){var T=e.unstable_now();N=T;var I=!0;try{I=C(!0,T)}finally{I?Kn():(E=!1,C=null)}}else E=!1}var Kn;if(typeof h=="function")Kn=function(){h(qn)};else if(typeof MessageChannel<"u"){var Pa=new MessageChannel,uh=Pa.port2;Pa.port1.onmessage=qn,Kn=function(){uh.postMessage(null)}}else Kn=function(){_(qn,0)};function Ls(T){C=T,E||(E=!0,Kn())}function As(T,I){O=_(function(){T(e.unstable_now())},I)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(T){T.callback=null},e.unstable_continueExecution=function(){m||g||(m=!0,Ls(k))},e.unstable_forceFrameRate=function(T){0>T||125<T?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):K=0<T?Math.floor(1e3/T):5},e.unstable_getCurrentPriorityLevel=function(){return f},e.unstable_getFirstCallbackNode=function(){return n(a)},e.unstable_next=function(T){switch(f){case 1:case 2:case 3:var I=3;break;default:I=f}var $=f;f=I;try{return T()}finally{f=$}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(T,I){switch(T){case 1:case 2:case 3:case 4:case 5:break;default:T=3}var $=f;f=T;try{return I()}finally{f=$}},e.unstable_scheduleCallback=function(T,I,$){var J=e.unstable_now();switch(typeof $=="object"&&$!==null?($=$.delay,$=typeof $=="number"&&0<$?J+$:J):$=J,T){case 1:var ee=-1;break;case 2:ee=250;break;case 5:ee=**********;break;case 4:ee=1e4;break;default:ee=5e3}return ee=$+ee,T={id:c++,callback:I,priorityLevel:T,startTime:$,expirationTime:ee,sortIndex:-1},$>J?(T.sortIndex=$,t(u,T),n(a)===null&&T===n(u)&&(w?(v(O),O=-1):w=!0,As(y,$-J))):(T.sortIndex=ee,t(a,T),m||g||(m=!0,Ls(k))),T},e.unstable_shouldYield=be,e.unstable_wrapCallback=function(T){var I=f;return function(){var $=f;f=I;try{return T.apply(this,arguments)}finally{f=$}}}})(Ec);kc.exports=Ec;var Ah=kc.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Nh=x,Pe=Ah;function S(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var xc=new Set,kr={};function en(e,t){On(e,t),On(e+"Capture",t)}function On(e,t){for(kr[e]=t,e=0;e<t.length;e++)xc.add(t[e])}var it=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),yo=Object.prototype.hasOwnProperty,bh=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Ra={},Ia={};function Uh(e){return yo.call(Ia,e)?!0:yo.call(Ra,e)?!1:bh.test(e)?Ia[e]=!0:(Ra[e]=!0,!1)}function Dh(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function zh(e,t,n,r){if(t===null||typeof t>"u"||Dh(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function ve(e,t,n,r,i,s,o){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=s,this.removeEmptyString=o}var oe={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){oe[e]=new ve(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];oe[t]=new ve(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){oe[e]=new ve(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){oe[e]=new ve(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){oe[e]=new ve(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){oe[e]=new ve(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){oe[e]=new ve(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){oe[e]=new ve(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){oe[e]=new ve(e,5,!1,e.toLowerCase(),null,!1,!1)});var Pl=/[\-:]([a-z])/g;function Tl(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Pl,Tl);oe[t]=new ve(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Pl,Tl);oe[t]=new ve(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Pl,Tl);oe[t]=new ve(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){oe[e]=new ve(e,1,!1,e.toLowerCase(),null,!1,!1)});oe.xlinkHref=new ve("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){oe[e]=new ve(e,1,!1,e.toLowerCase(),null,!0,!0)});function jl(e,t,n,r){var i=oe.hasOwnProperty(t)?oe[t]:null;(i!==null?i.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(zh(t,n,i,r)&&(n=null),r||i===null?Uh(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):i.mustUseProperty?e[i.propertyName]=n===null?i.type===3?!1:"":n:(t=i.attributeName,r=i.attributeNamespace,n===null?e.removeAttribute(t):(i=i.type,n=i===3||i===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var at=Nh.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Zr=Symbol.for("react.element"),cn=Symbol.for("react.portal"),dn=Symbol.for("react.fragment"),Ol=Symbol.for("react.strict_mode"),wo=Symbol.for("react.profiler"),Cc=Symbol.for("react.provider"),Pc=Symbol.for("react.context"),Rl=Symbol.for("react.forward_ref"),_o=Symbol.for("react.suspense"),So=Symbol.for("react.suspense_list"),Il=Symbol.for("react.memo"),dt=Symbol.for("react.lazy"),Tc=Symbol.for("react.offscreen"),$a=Symbol.iterator;function Jn(e){return e===null||typeof e!="object"?null:(e=$a&&e[$a]||e["@@iterator"],typeof e=="function"?e:null)}var H=Object.assign,Us;function rr(e){if(Us===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Us=t&&t[1]||""}return`
`+Us+e}var Ds=!1;function zs(e,t){if(!e||Ds)return"";Ds=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var i=u.stack.split(`
`),s=r.stack.split(`
`),o=i.length-1,l=s.length-1;1<=o&&0<=l&&i[o]!==s[l];)l--;for(;1<=o&&0<=l;o--,l--)if(i[o]!==s[l]){if(o!==1||l!==1)do if(o--,l--,0>l||i[o]!==s[l]){var a=`
`+i[o].replace(" at new "," at ");return e.displayName&&a.includes("<anonymous>")&&(a=a.replace("<anonymous>",e.displayName)),a}while(1<=o&&0<=l);break}}}finally{Ds=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?rr(e):""}function Mh(e){switch(e.tag){case 5:return rr(e.type);case 16:return rr("Lazy");case 13:return rr("Suspense");case 19:return rr("SuspenseList");case 0:case 2:case 15:return e=zs(e.type,!1),e;case 11:return e=zs(e.type.render,!1),e;case 1:return e=zs(e.type,!0),e;default:return""}}function ko(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case dn:return"Fragment";case cn:return"Portal";case wo:return"Profiler";case Ol:return"StrictMode";case _o:return"Suspense";case So:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Pc:return(e.displayName||"Context")+".Consumer";case Cc:return(e._context.displayName||"Context")+".Provider";case Rl:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Il:return t=e.displayName||null,t!==null?t:ko(e.type)||"Memo";case dt:t=e._payload,e=e._init;try{return ko(e(t))}catch{}}return null}function Fh(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return ko(t);case 8:return t===Ol?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Ot(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function jc(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Bh(e){var t=jc(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,s=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(o){r=""+o,s.call(this,o)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(o){r=""+o},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function ei(e){e._valueTracker||(e._valueTracker=Bh(e))}function Oc(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=jc(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Li(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Eo(e,t){var n=t.checked;return H({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function La(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=Ot(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Rc(e,t){t=t.checked,t!=null&&jl(e,"checked",t,!1)}function xo(e,t){Rc(e,t);var n=Ot(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Co(e,t.type,n):t.hasOwnProperty("defaultValue")&&Co(e,t.type,Ot(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Aa(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Co(e,t,n){(t!=="number"||Li(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var ir=Array.isArray;function En(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Ot(n),t=null,i=0;i<e.length;i++){if(e[i].value===n){e[i].selected=!0,r&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function Po(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(S(91));return H({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Na(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(S(92));if(ir(n)){if(1<n.length)throw Error(S(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Ot(n)}}function Ic(e,t){var n=Ot(t.value),r=Ot(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function ba(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function $c(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function To(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?$c(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var ti,Lc=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,i){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,i)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(ti=ti||document.createElement("div"),ti.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ti.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Er(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var ur={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Wh=["Webkit","ms","Moz","O"];Object.keys(ur).forEach(function(e){Wh.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),ur[t]=ur[e]})});function Ac(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||ur.hasOwnProperty(e)&&ur[e]?(""+t).trim():t+"px"}function Nc(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,i=Ac(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,i):e[n]=i}}var Vh=H({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function jo(e,t){if(t){if(Vh[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(S(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(S(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(S(61))}if(t.style!=null&&typeof t.style!="object")throw Error(S(62))}}function Oo(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Ro=null;function $l(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Io=null,xn=null,Cn=null;function Ua(e){if(e=Hr(e)){if(typeof Io!="function")throw Error(S(280));var t=e.stateNode;t&&(t=fs(t),Io(e.stateNode,e.type,t))}}function bc(e){xn?Cn?Cn.push(e):Cn=[e]:xn=e}function Uc(){if(xn){var e=xn,t=Cn;if(Cn=xn=null,Ua(e),t)for(e=0;e<t.length;e++)Ua(t[e])}}function Dc(e,t){return e(t)}function zc(){}var Ms=!1;function Mc(e,t,n){if(Ms)return e(t,n);Ms=!0;try{return Dc(e,t,n)}finally{Ms=!1,(xn!==null||Cn!==null)&&(zc(),Uc())}}function xr(e,t){var n=e.stateNode;if(n===null)return null;var r=fs(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(S(231,t,typeof n));return n}var $o=!1;if(it)try{var Qn={};Object.defineProperty(Qn,"passive",{get:function(){$o=!0}}),window.addEventListener("test",Qn,Qn),window.removeEventListener("test",Qn,Qn)}catch{$o=!1}function Hh(e,t,n,r,i,s,o,l,a){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var cr=!1,Ai=null,Ni=!1,Lo=null,qh={onError:function(e){cr=!0,Ai=e}};function Kh(e,t,n,r,i,s,o,l,a){cr=!1,Ai=null,Hh.apply(qh,arguments)}function Jh(e,t,n,r,i,s,o,l,a){if(Kh.apply(this,arguments),cr){if(cr){var u=Ai;cr=!1,Ai=null}else throw Error(S(198));Ni||(Ni=!0,Lo=u)}}function tn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Fc(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Da(e){if(tn(e)!==e)throw Error(S(188))}function Qh(e){var t=e.alternate;if(!t){if(t=tn(e),t===null)throw Error(S(188));return t!==e?null:e}for(var n=e,r=t;;){var i=n.return;if(i===null)break;var s=i.alternate;if(s===null){if(r=i.return,r!==null){n=r;continue}break}if(i.child===s.child){for(s=i.child;s;){if(s===n)return Da(i),e;if(s===r)return Da(i),t;s=s.sibling}throw Error(S(188))}if(n.return!==r.return)n=i,r=s;else{for(var o=!1,l=i.child;l;){if(l===n){o=!0,n=i,r=s;break}if(l===r){o=!0,r=i,n=s;break}l=l.sibling}if(!o){for(l=s.child;l;){if(l===n){o=!0,n=s,r=i;break}if(l===r){o=!0,r=s,n=i;break}l=l.sibling}if(!o)throw Error(S(189))}}if(n.alternate!==r)throw Error(S(190))}if(n.tag!==3)throw Error(S(188));return n.stateNode.current===n?e:t}function Bc(e){return e=Qh(e),e!==null?Wc(e):null}function Wc(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Wc(e);if(t!==null)return t;e=e.sibling}return null}var Vc=Pe.unstable_scheduleCallback,za=Pe.unstable_cancelCallback,Gh=Pe.unstable_shouldYield,Yh=Pe.unstable_requestPaint,Q=Pe.unstable_now,Xh=Pe.unstable_getCurrentPriorityLevel,Ll=Pe.unstable_ImmediatePriority,Hc=Pe.unstable_UserBlockingPriority,bi=Pe.unstable_NormalPriority,Zh=Pe.unstable_LowPriority,qc=Pe.unstable_IdlePriority,as=null,Qe=null;function ep(e){if(Qe&&typeof Qe.onCommitFiberRoot=="function")try{Qe.onCommitFiberRoot(as,e,void 0,(e.current.flags&128)===128)}catch{}}var We=Math.clz32?Math.clz32:rp,tp=Math.log,np=Math.LN2;function rp(e){return e>>>=0,e===0?32:31-(tp(e)/np|0)|0}var ni=64,ri=4194304;function sr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Ui(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,i=e.suspendedLanes,s=e.pingedLanes,o=n&268435455;if(o!==0){var l=o&~i;l!==0?r=sr(l):(s&=o,s!==0&&(r=sr(s)))}else o=n&~i,o!==0?r=sr(o):s!==0&&(r=sr(s));if(r===0)return 0;if(t!==0&&t!==r&&!(t&i)&&(i=r&-r,s=t&-t,i>=s||i===16&&(s&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-We(t),i=1<<n,r|=e[n],t&=~i;return r}function ip(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function sp(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,s=e.pendingLanes;0<s;){var o=31-We(s),l=1<<o,a=i[o];a===-1?(!(l&n)||l&r)&&(i[o]=ip(l,t)):a<=t&&(e.expiredLanes|=l),s&=~l}}function Ao(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Kc(){var e=ni;return ni<<=1,!(ni&4194240)&&(ni=64),e}function Fs(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Wr(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-We(t),e[t]=n}function op(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var i=31-We(n),s=1<<i;t[i]=0,r[i]=-1,e[i]=-1,n&=~s}}function Al(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-We(n),i=1<<r;i&t|e[r]&t&&(e[r]|=t),n&=~i}}var U=0;function Jc(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Qc,Nl,Gc,Yc,Xc,No=!1,ii=[],_t=null,St=null,kt=null,Cr=new Map,Pr=new Map,pt=[],lp="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Ma(e,t){switch(e){case"focusin":case"focusout":_t=null;break;case"dragenter":case"dragleave":St=null;break;case"mouseover":case"mouseout":kt=null;break;case"pointerover":case"pointerout":Cr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Pr.delete(t.pointerId)}}function Gn(e,t,n,r,i,s){return e===null||e.nativeEvent!==s?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:s,targetContainers:[i]},t!==null&&(t=Hr(t),t!==null&&Nl(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function ap(e,t,n,r,i){switch(t){case"focusin":return _t=Gn(_t,e,t,n,r,i),!0;case"dragenter":return St=Gn(St,e,t,n,r,i),!0;case"mouseover":return kt=Gn(kt,e,t,n,r,i),!0;case"pointerover":var s=i.pointerId;return Cr.set(s,Gn(Cr.get(s)||null,e,t,n,r,i)),!0;case"gotpointercapture":return s=i.pointerId,Pr.set(s,Gn(Pr.get(s)||null,e,t,n,r,i)),!0}return!1}function Zc(e){var t=Vt(e.target);if(t!==null){var n=tn(t);if(n!==null){if(t=n.tag,t===13){if(t=Fc(n),t!==null){e.blockedOn=t,Xc(e.priority,function(){Gc(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Ei(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=bo(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Ro=r,n.target.dispatchEvent(r),Ro=null}else return t=Hr(n),t!==null&&Nl(t),e.blockedOn=n,!1;t.shift()}return!0}function Fa(e,t,n){Ei(e)&&n.delete(t)}function up(){No=!1,_t!==null&&Ei(_t)&&(_t=null),St!==null&&Ei(St)&&(St=null),kt!==null&&Ei(kt)&&(kt=null),Cr.forEach(Fa),Pr.forEach(Fa)}function Yn(e,t){e.blockedOn===t&&(e.blockedOn=null,No||(No=!0,Pe.unstable_scheduleCallback(Pe.unstable_NormalPriority,up)))}function Tr(e){function t(i){return Yn(i,e)}if(0<ii.length){Yn(ii[0],e);for(var n=1;n<ii.length;n++){var r=ii[n];r.blockedOn===e&&(r.blockedOn=null)}}for(_t!==null&&Yn(_t,e),St!==null&&Yn(St,e),kt!==null&&Yn(kt,e),Cr.forEach(t),Pr.forEach(t),n=0;n<pt.length;n++)r=pt[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<pt.length&&(n=pt[0],n.blockedOn===null);)Zc(n),n.blockedOn===null&&pt.shift()}var Pn=at.ReactCurrentBatchConfig,Di=!0;function cp(e,t,n,r){var i=U,s=Pn.transition;Pn.transition=null;try{U=1,bl(e,t,n,r)}finally{U=i,Pn.transition=s}}function dp(e,t,n,r){var i=U,s=Pn.transition;Pn.transition=null;try{U=4,bl(e,t,n,r)}finally{U=i,Pn.transition=s}}function bl(e,t,n,r){if(Di){var i=bo(e,t,n,r);if(i===null)Ys(e,t,r,zi,n),Ma(e,r);else if(ap(i,e,t,n,r))r.stopPropagation();else if(Ma(e,r),t&4&&-1<lp.indexOf(e)){for(;i!==null;){var s=Hr(i);if(s!==null&&Qc(s),s=bo(e,t,n,r),s===null&&Ys(e,t,r,zi,n),s===i)break;i=s}i!==null&&r.stopPropagation()}else Ys(e,t,r,null,n)}}var zi=null;function bo(e,t,n,r){if(zi=null,e=$l(r),e=Vt(e),e!==null)if(t=tn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Fc(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return zi=e,null}function ed(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Xh()){case Ll:return 1;case Hc:return 4;case bi:case Zh:return 16;case qc:return 536870912;default:return 16}default:return 16}}var mt=null,Ul=null,xi=null;function td(){if(xi)return xi;var e,t=Ul,n=t.length,r,i="value"in mt?mt.value:mt.textContent,s=i.length;for(e=0;e<n&&t[e]===i[e];e++);var o=n-e;for(r=1;r<=o&&t[n-r]===i[s-r];r++);return xi=i.slice(e,1<r?1-r:void 0)}function Ci(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function si(){return!0}function Ba(){return!1}function je(e){function t(n,r,i,s,o){this._reactName=n,this._targetInst=i,this.type=r,this.nativeEvent=s,this.target=o,this.currentTarget=null;for(var l in e)e.hasOwnProperty(l)&&(n=e[l],this[l]=n?n(s):s[l]);return this.isDefaultPrevented=(s.defaultPrevented!=null?s.defaultPrevented:s.returnValue===!1)?si:Ba,this.isPropagationStopped=Ba,this}return H(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=si)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=si)},persist:function(){},isPersistent:si}),t}var zn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Dl=je(zn),Vr=H({},zn,{view:0,detail:0}),fp=je(Vr),Bs,Ws,Xn,us=H({},Vr,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:zl,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Xn&&(Xn&&e.type==="mousemove"?(Bs=e.screenX-Xn.screenX,Ws=e.screenY-Xn.screenY):Ws=Bs=0,Xn=e),Bs)},movementY:function(e){return"movementY"in e?e.movementY:Ws}}),Wa=je(us),hp=H({},us,{dataTransfer:0}),pp=je(hp),vp=H({},Vr,{relatedTarget:0}),Vs=je(vp),gp=H({},zn,{animationName:0,elapsedTime:0,pseudoElement:0}),mp=je(gp),yp=H({},zn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),wp=je(yp),_p=H({},zn,{data:0}),Va=je(_p),Sp={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},kp={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Ep={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function xp(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Ep[e])?!!t[e]:!1}function zl(){return xp}var Cp=H({},Vr,{key:function(e){if(e.key){var t=Sp[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Ci(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?kp[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:zl,charCode:function(e){return e.type==="keypress"?Ci(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Ci(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Pp=je(Cp),Tp=H({},us,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Ha=je(Tp),jp=H({},Vr,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:zl}),Op=je(jp),Rp=H({},zn,{propertyName:0,elapsedTime:0,pseudoElement:0}),Ip=je(Rp),$p=H({},us,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Lp=je($p),Ap=[9,13,27,32],Ml=it&&"CompositionEvent"in window,dr=null;it&&"documentMode"in document&&(dr=document.documentMode);var Np=it&&"TextEvent"in window&&!dr,nd=it&&(!Ml||dr&&8<dr&&11>=dr),qa=String.fromCharCode(32),Ka=!1;function rd(e,t){switch(e){case"keyup":return Ap.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function id(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var fn=!1;function bp(e,t){switch(e){case"compositionend":return id(t);case"keypress":return t.which!==32?null:(Ka=!0,qa);case"textInput":return e=t.data,e===qa&&Ka?null:e;default:return null}}function Up(e,t){if(fn)return e==="compositionend"||!Ml&&rd(e,t)?(e=td(),xi=Ul=mt=null,fn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return nd&&t.locale!=="ko"?null:t.data;default:return null}}var Dp={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Ja(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Dp[e.type]:t==="textarea"}function sd(e,t,n,r){bc(r),t=Mi(t,"onChange"),0<t.length&&(n=new Dl("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var fr=null,jr=null;function zp(e){gd(e,0)}function cs(e){var t=vn(e);if(Oc(t))return e}function Mp(e,t){if(e==="change")return t}var od=!1;if(it){var Hs;if(it){var qs="oninput"in document;if(!qs){var Qa=document.createElement("div");Qa.setAttribute("oninput","return;"),qs=typeof Qa.oninput=="function"}Hs=qs}else Hs=!1;od=Hs&&(!document.documentMode||9<document.documentMode)}function Ga(){fr&&(fr.detachEvent("onpropertychange",ld),jr=fr=null)}function ld(e){if(e.propertyName==="value"&&cs(jr)){var t=[];sd(t,jr,e,$l(e)),Mc(zp,t)}}function Fp(e,t,n){e==="focusin"?(Ga(),fr=t,jr=n,fr.attachEvent("onpropertychange",ld)):e==="focusout"&&Ga()}function Bp(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return cs(jr)}function Wp(e,t){if(e==="click")return cs(t)}function Vp(e,t){if(e==="input"||e==="change")return cs(t)}function Hp(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var He=typeof Object.is=="function"?Object.is:Hp;function Or(e,t){if(He(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!yo.call(t,i)||!He(e[i],t[i]))return!1}return!0}function Ya(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Xa(e,t){var n=Ya(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Ya(n)}}function ad(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?ad(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function ud(){for(var e=window,t=Li();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Li(e.document)}return t}function Fl(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function qp(e){var t=ud(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&ad(n.ownerDocument.documentElement,n)){if(r!==null&&Fl(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var i=n.textContent.length,s=Math.min(r.start,i);r=r.end===void 0?s:Math.min(r.end,i),!e.extend&&s>r&&(i=r,r=s,s=i),i=Xa(n,s);var o=Xa(n,r);i&&o&&(e.rangeCount!==1||e.anchorNode!==i.node||e.anchorOffset!==i.offset||e.focusNode!==o.node||e.focusOffset!==o.offset)&&(t=t.createRange(),t.setStart(i.node,i.offset),e.removeAllRanges(),s>r?(e.addRange(t),e.extend(o.node,o.offset)):(t.setEnd(o.node,o.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Kp=it&&"documentMode"in document&&11>=document.documentMode,hn=null,Uo=null,hr=null,Do=!1;function Za(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Do||hn==null||hn!==Li(r)||(r=hn,"selectionStart"in r&&Fl(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),hr&&Or(hr,r)||(hr=r,r=Mi(Uo,"onSelect"),0<r.length&&(t=new Dl("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=hn)))}function oi(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var pn={animationend:oi("Animation","AnimationEnd"),animationiteration:oi("Animation","AnimationIteration"),animationstart:oi("Animation","AnimationStart"),transitionend:oi("Transition","TransitionEnd")},Ks={},cd={};it&&(cd=document.createElement("div").style,"AnimationEvent"in window||(delete pn.animationend.animation,delete pn.animationiteration.animation,delete pn.animationstart.animation),"TransitionEvent"in window||delete pn.transitionend.transition);function ds(e){if(Ks[e])return Ks[e];if(!pn[e])return e;var t=pn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in cd)return Ks[e]=t[n];return e}var dd=ds("animationend"),fd=ds("animationiteration"),hd=ds("animationstart"),pd=ds("transitionend"),vd=new Map,eu="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function It(e,t){vd.set(e,t),en(t,[e])}for(var Js=0;Js<eu.length;Js++){var Qs=eu[Js],Jp=Qs.toLowerCase(),Qp=Qs[0].toUpperCase()+Qs.slice(1);It(Jp,"on"+Qp)}It(dd,"onAnimationEnd");It(fd,"onAnimationIteration");It(hd,"onAnimationStart");It("dblclick","onDoubleClick");It("focusin","onFocus");It("focusout","onBlur");It(pd,"onTransitionEnd");On("onMouseEnter",["mouseout","mouseover"]);On("onMouseLeave",["mouseout","mouseover"]);On("onPointerEnter",["pointerout","pointerover"]);On("onPointerLeave",["pointerout","pointerover"]);en("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));en("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));en("onBeforeInput",["compositionend","keypress","textInput","paste"]);en("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));en("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));en("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var or="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Gp=new Set("cancel close invalid load scroll toggle".split(" ").concat(or));function tu(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Jh(r,t,void 0,e),e.currentTarget=null}function gd(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],i=r.event;r=r.listeners;e:{var s=void 0;if(t)for(var o=r.length-1;0<=o;o--){var l=r[o],a=l.instance,u=l.currentTarget;if(l=l.listener,a!==s&&i.isPropagationStopped())break e;tu(i,l,u),s=a}else for(o=0;o<r.length;o++){if(l=r[o],a=l.instance,u=l.currentTarget,l=l.listener,a!==s&&i.isPropagationStopped())break e;tu(i,l,u),s=a}}}if(Ni)throw e=Lo,Ni=!1,Lo=null,e}function M(e,t){var n=t[Wo];n===void 0&&(n=t[Wo]=new Set);var r=e+"__bubble";n.has(r)||(md(t,e,2,!1),n.add(r))}function Gs(e,t,n){var r=0;t&&(r|=4),md(n,e,r,t)}var li="_reactListening"+Math.random().toString(36).slice(2);function Rr(e){if(!e[li]){e[li]=!0,xc.forEach(function(n){n!=="selectionchange"&&(Gp.has(n)||Gs(n,!1,e),Gs(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[li]||(t[li]=!0,Gs("selectionchange",!1,t))}}function md(e,t,n,r){switch(ed(t)){case 1:var i=cp;break;case 4:i=dp;break;default:i=bl}n=i.bind(null,t,n,e),i=void 0,!$o||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),r?i!==void 0?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):i!==void 0?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function Ys(e,t,n,r,i){var s=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var o=r.tag;if(o===3||o===4){var l=r.stateNode.containerInfo;if(l===i||l.nodeType===8&&l.parentNode===i)break;if(o===4)for(o=r.return;o!==null;){var a=o.tag;if((a===3||a===4)&&(a=o.stateNode.containerInfo,a===i||a.nodeType===8&&a.parentNode===i))return;o=o.return}for(;l!==null;){if(o=Vt(l),o===null)return;if(a=o.tag,a===5||a===6){r=s=o;continue e}l=l.parentNode}}r=r.return}Mc(function(){var u=s,c=$l(n),d=[];e:{var f=vd.get(e);if(f!==void 0){var g=Dl,m=e;switch(e){case"keypress":if(Ci(n)===0)break e;case"keydown":case"keyup":g=Pp;break;case"focusin":m="focus",g=Vs;break;case"focusout":m="blur",g=Vs;break;case"beforeblur":case"afterblur":g=Vs;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":g=Wa;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":g=pp;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":g=Op;break;case dd:case fd:case hd:g=mp;break;case pd:g=Ip;break;case"scroll":g=fp;break;case"wheel":g=Lp;break;case"copy":case"cut":case"paste":g=wp;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":g=Ha}var w=(t&4)!==0,_=!w&&e==="scroll",v=w?f!==null?f+"Capture":null:f;w=[];for(var h=u,p;h!==null;){p=h;var y=p.stateNode;if(p.tag===5&&y!==null&&(p=y,v!==null&&(y=xr(h,v),y!=null&&w.push(Ir(h,y,p)))),_)break;h=h.return}0<w.length&&(f=new g(f,m,null,n,c),d.push({event:f,listeners:w}))}}if(!(t&7)){e:{if(f=e==="mouseover"||e==="pointerover",g=e==="mouseout"||e==="pointerout",f&&n!==Ro&&(m=n.relatedTarget||n.fromElement)&&(Vt(m)||m[st]))break e;if((g||f)&&(f=c.window===c?c:(f=c.ownerDocument)?f.defaultView||f.parentWindow:window,g?(m=n.relatedTarget||n.toElement,g=u,m=m?Vt(m):null,m!==null&&(_=tn(m),m!==_||m.tag!==5&&m.tag!==6)&&(m=null)):(g=null,m=u),g!==m)){if(w=Wa,y="onMouseLeave",v="onMouseEnter",h="mouse",(e==="pointerout"||e==="pointerover")&&(w=Ha,y="onPointerLeave",v="onPointerEnter",h="pointer"),_=g==null?f:vn(g),p=m==null?f:vn(m),f=new w(y,h+"leave",g,n,c),f.target=_,f.relatedTarget=p,y=null,Vt(c)===u&&(w=new w(v,h+"enter",m,n,c),w.target=p,w.relatedTarget=_,y=w),_=y,g&&m)t:{for(w=g,v=m,h=0,p=w;p;p=rn(p))h++;for(p=0,y=v;y;y=rn(y))p++;for(;0<h-p;)w=rn(w),h--;for(;0<p-h;)v=rn(v),p--;for(;h--;){if(w===v||v!==null&&w===v.alternate)break t;w=rn(w),v=rn(v)}w=null}else w=null;g!==null&&nu(d,f,g,w,!1),m!==null&&_!==null&&nu(d,_,m,w,!0)}}e:{if(f=u?vn(u):window,g=f.nodeName&&f.nodeName.toLowerCase(),g==="select"||g==="input"&&f.type==="file")var k=Mp;else if(Ja(f))if(od)k=Vp;else{k=Bp;var E=Fp}else(g=f.nodeName)&&g.toLowerCase()==="input"&&(f.type==="checkbox"||f.type==="radio")&&(k=Wp);if(k&&(k=k(e,u))){sd(d,k,n,c);break e}E&&E(e,f,u),e==="focusout"&&(E=f._wrapperState)&&E.controlled&&f.type==="number"&&Co(f,"number",f.value)}switch(E=u?vn(u):window,e){case"focusin":(Ja(E)||E.contentEditable==="true")&&(hn=E,Uo=u,hr=null);break;case"focusout":hr=Uo=hn=null;break;case"mousedown":Do=!0;break;case"contextmenu":case"mouseup":case"dragend":Do=!1,Za(d,n,c);break;case"selectionchange":if(Kp)break;case"keydown":case"keyup":Za(d,n,c)}var C;if(Ml)e:{switch(e){case"compositionstart":var O="onCompositionStart";break e;case"compositionend":O="onCompositionEnd";break e;case"compositionupdate":O="onCompositionUpdate";break e}O=void 0}else fn?rd(e,n)&&(O="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(O="onCompositionStart");O&&(nd&&n.locale!=="ko"&&(fn||O!=="onCompositionStart"?O==="onCompositionEnd"&&fn&&(C=td()):(mt=c,Ul="value"in mt?mt.value:mt.textContent,fn=!0)),E=Mi(u,O),0<E.length&&(O=new Va(O,e,null,n,c),d.push({event:O,listeners:E}),C?O.data=C:(C=id(n),C!==null&&(O.data=C)))),(C=Np?bp(e,n):Up(e,n))&&(u=Mi(u,"onBeforeInput"),0<u.length&&(c=new Va("onBeforeInput","beforeinput",null,n,c),d.push({event:c,listeners:u}),c.data=C))}gd(d,t)})}function Ir(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Mi(e,t){for(var n=t+"Capture",r=[];e!==null;){var i=e,s=i.stateNode;i.tag===5&&s!==null&&(i=s,s=xr(e,n),s!=null&&r.unshift(Ir(e,s,i)),s=xr(e,t),s!=null&&r.push(Ir(e,s,i))),e=e.return}return r}function rn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function nu(e,t,n,r,i){for(var s=t._reactName,o=[];n!==null&&n!==r;){var l=n,a=l.alternate,u=l.stateNode;if(a!==null&&a===r)break;l.tag===5&&u!==null&&(l=u,i?(a=xr(n,s),a!=null&&o.unshift(Ir(n,a,l))):i||(a=xr(n,s),a!=null&&o.push(Ir(n,a,l)))),n=n.return}o.length!==0&&e.push({event:t,listeners:o})}var Yp=/\r\n?/g,Xp=/\u0000|\uFFFD/g;function ru(e){return(typeof e=="string"?e:""+e).replace(Yp,`
`).replace(Xp,"")}function ai(e,t,n){if(t=ru(t),ru(e)!==t&&n)throw Error(S(425))}function Fi(){}var zo=null,Mo=null;function Fo(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Bo=typeof setTimeout=="function"?setTimeout:void 0,Zp=typeof clearTimeout=="function"?clearTimeout:void 0,iu=typeof Promise=="function"?Promise:void 0,ev=typeof queueMicrotask=="function"?queueMicrotask:typeof iu<"u"?function(e){return iu.resolve(null).then(e).catch(tv)}:Bo;function tv(e){setTimeout(function(){throw e})}function Xs(e,t){var n=t,r=0;do{var i=n.nextSibling;if(e.removeChild(n),i&&i.nodeType===8)if(n=i.data,n==="/$"){if(r===0){e.removeChild(i),Tr(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=i}while(n);Tr(t)}function Et(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function su(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Mn=Math.random().toString(36).slice(2),Je="__reactFiber$"+Mn,$r="__reactProps$"+Mn,st="__reactContainer$"+Mn,Wo="__reactEvents$"+Mn,nv="__reactListeners$"+Mn,rv="__reactHandles$"+Mn;function Vt(e){var t=e[Je];if(t)return t;for(var n=e.parentNode;n;){if(t=n[st]||n[Je]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=su(e);e!==null;){if(n=e[Je])return n;e=su(e)}return t}e=n,n=e.parentNode}return null}function Hr(e){return e=e[Je]||e[st],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function vn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(S(33))}function fs(e){return e[$r]||null}var Vo=[],gn=-1;function $t(e){return{current:e}}function F(e){0>gn||(e.current=Vo[gn],Vo[gn]=null,gn--)}function z(e,t){gn++,Vo[gn]=e.current,e.current=t}var Rt={},de=$t(Rt),_e=$t(!1),Qt=Rt;function Rn(e,t){var n=e.type.contextTypes;if(!n)return Rt;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i={},s;for(s in n)i[s]=t[s];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function Se(e){return e=e.childContextTypes,e!=null}function Bi(){F(_e),F(de)}function ou(e,t,n){if(de.current!==Rt)throw Error(S(168));z(de,t),z(_e,n)}function yd(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var i in r)if(!(i in t))throw Error(S(108,Fh(e)||"Unknown",i));return H({},n,r)}function Wi(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Rt,Qt=de.current,z(de,e),z(_e,_e.current),!0}function lu(e,t,n){var r=e.stateNode;if(!r)throw Error(S(169));n?(e=yd(e,t,Qt),r.__reactInternalMemoizedMergedChildContext=e,F(_e),F(de),z(de,e)):F(_e),z(_e,n)}var et=null,hs=!1,Zs=!1;function wd(e){et===null?et=[e]:et.push(e)}function iv(e){hs=!0,wd(e)}function Lt(){if(!Zs&&et!==null){Zs=!0;var e=0,t=U;try{var n=et;for(U=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}et=null,hs=!1}catch(i){throw et!==null&&(et=et.slice(e+1)),Vc(Ll,Lt),i}finally{U=t,Zs=!1}}return null}var mn=[],yn=0,Vi=null,Hi=0,Oe=[],Re=0,Gt=null,tt=1,nt="";function Mt(e,t){mn[yn++]=Hi,mn[yn++]=Vi,Vi=e,Hi=t}function _d(e,t,n){Oe[Re++]=tt,Oe[Re++]=nt,Oe[Re++]=Gt,Gt=e;var r=tt;e=nt;var i=32-We(r)-1;r&=~(1<<i),n+=1;var s=32-We(t)+i;if(30<s){var o=i-i%5;s=(r&(1<<o)-1).toString(32),r>>=o,i-=o,tt=1<<32-We(t)+i|n<<i|r,nt=s+e}else tt=1<<s|n<<i|r,nt=e}function Bl(e){e.return!==null&&(Mt(e,1),_d(e,1,0))}function Wl(e){for(;e===Vi;)Vi=mn[--yn],mn[yn]=null,Hi=mn[--yn],mn[yn]=null;for(;e===Gt;)Gt=Oe[--Re],Oe[Re]=null,nt=Oe[--Re],Oe[Re]=null,tt=Oe[--Re],Oe[Re]=null}var Ce=null,xe=null,B=!1,Be=null;function Sd(e,t){var n=Ie(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function au(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Ce=e,xe=Et(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Ce=e,xe=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=Gt!==null?{id:tt,overflow:nt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=Ie(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Ce=e,xe=null,!0):!1;default:return!1}}function Ho(e){return(e.mode&1)!==0&&(e.flags&128)===0}function qo(e){if(B){var t=xe;if(t){var n=t;if(!au(e,t)){if(Ho(e))throw Error(S(418));t=Et(n.nextSibling);var r=Ce;t&&au(e,t)?Sd(r,n):(e.flags=e.flags&-4097|2,B=!1,Ce=e)}}else{if(Ho(e))throw Error(S(418));e.flags=e.flags&-4097|2,B=!1,Ce=e}}}function uu(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Ce=e}function ui(e){if(e!==Ce)return!1;if(!B)return uu(e),B=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Fo(e.type,e.memoizedProps)),t&&(t=xe)){if(Ho(e))throw kd(),Error(S(418));for(;t;)Sd(e,t),t=Et(t.nextSibling)}if(uu(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(S(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){xe=Et(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}xe=null}}else xe=Ce?Et(e.stateNode.nextSibling):null;return!0}function kd(){for(var e=xe;e;)e=Et(e.nextSibling)}function In(){xe=Ce=null,B=!1}function Vl(e){Be===null?Be=[e]:Be.push(e)}var sv=at.ReactCurrentBatchConfig;function Zn(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(S(309));var r=n.stateNode}if(!r)throw Error(S(147,e));var i=r,s=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===s?t.ref:(t=function(o){var l=i.refs;o===null?delete l[s]:l[s]=o},t._stringRef=s,t)}if(typeof e!="string")throw Error(S(284));if(!n._owner)throw Error(S(290,e))}return e}function ci(e,t){throw e=Object.prototype.toString.call(t),Error(S(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function cu(e){var t=e._init;return t(e._payload)}function Ed(e){function t(v,h){if(e){var p=v.deletions;p===null?(v.deletions=[h],v.flags|=16):p.push(h)}}function n(v,h){if(!e)return null;for(;h!==null;)t(v,h),h=h.sibling;return null}function r(v,h){for(v=new Map;h!==null;)h.key!==null?v.set(h.key,h):v.set(h.index,h),h=h.sibling;return v}function i(v,h){return v=Tt(v,h),v.index=0,v.sibling=null,v}function s(v,h,p){return v.index=p,e?(p=v.alternate,p!==null?(p=p.index,p<h?(v.flags|=2,h):p):(v.flags|=2,h)):(v.flags|=1048576,h)}function o(v){return e&&v.alternate===null&&(v.flags|=2),v}function l(v,h,p,y){return h===null||h.tag!==6?(h=oo(p,v.mode,y),h.return=v,h):(h=i(h,p),h.return=v,h)}function a(v,h,p,y){var k=p.type;return k===dn?c(v,h,p.props.children,y,p.key):h!==null&&(h.elementType===k||typeof k=="object"&&k!==null&&k.$$typeof===dt&&cu(k)===h.type)?(y=i(h,p.props),y.ref=Zn(v,h,p),y.return=v,y):(y=$i(p.type,p.key,p.props,null,v.mode,y),y.ref=Zn(v,h,p),y.return=v,y)}function u(v,h,p,y){return h===null||h.tag!==4||h.stateNode.containerInfo!==p.containerInfo||h.stateNode.implementation!==p.implementation?(h=lo(p,v.mode,y),h.return=v,h):(h=i(h,p.children||[]),h.return=v,h)}function c(v,h,p,y,k){return h===null||h.tag!==7?(h=Jt(p,v.mode,y,k),h.return=v,h):(h=i(h,p),h.return=v,h)}function d(v,h,p){if(typeof h=="string"&&h!==""||typeof h=="number")return h=oo(""+h,v.mode,p),h.return=v,h;if(typeof h=="object"&&h!==null){switch(h.$$typeof){case Zr:return p=$i(h.type,h.key,h.props,null,v.mode,p),p.ref=Zn(v,null,h),p.return=v,p;case cn:return h=lo(h,v.mode,p),h.return=v,h;case dt:var y=h._init;return d(v,y(h._payload),p)}if(ir(h)||Jn(h))return h=Jt(h,v.mode,p,null),h.return=v,h;ci(v,h)}return null}function f(v,h,p,y){var k=h!==null?h.key:null;if(typeof p=="string"&&p!==""||typeof p=="number")return k!==null?null:l(v,h,""+p,y);if(typeof p=="object"&&p!==null){switch(p.$$typeof){case Zr:return p.key===k?a(v,h,p,y):null;case cn:return p.key===k?u(v,h,p,y):null;case dt:return k=p._init,f(v,h,k(p._payload),y)}if(ir(p)||Jn(p))return k!==null?null:c(v,h,p,y,null);ci(v,p)}return null}function g(v,h,p,y,k){if(typeof y=="string"&&y!==""||typeof y=="number")return v=v.get(p)||null,l(h,v,""+y,k);if(typeof y=="object"&&y!==null){switch(y.$$typeof){case Zr:return v=v.get(y.key===null?p:y.key)||null,a(h,v,y,k);case cn:return v=v.get(y.key===null?p:y.key)||null,u(h,v,y,k);case dt:var E=y._init;return g(v,h,p,E(y._payload),k)}if(ir(y)||Jn(y))return v=v.get(p)||null,c(h,v,y,k,null);ci(h,y)}return null}function m(v,h,p,y){for(var k=null,E=null,C=h,O=h=0,K=null;C!==null&&O<p.length;O++){C.index>O?(K=C,C=null):K=C.sibling;var N=f(v,C,p[O],y);if(N===null){C===null&&(C=K);break}e&&C&&N.alternate===null&&t(v,C),h=s(N,h,O),E===null?k=N:E.sibling=N,E=N,C=K}if(O===p.length)return n(v,C),B&&Mt(v,O),k;if(C===null){for(;O<p.length;O++)C=d(v,p[O],y),C!==null&&(h=s(C,h,O),E===null?k=C:E.sibling=C,E=C);return B&&Mt(v,O),k}for(C=r(v,C);O<p.length;O++)K=g(C,v,O,p[O],y),K!==null&&(e&&K.alternate!==null&&C.delete(K.key===null?O:K.key),h=s(K,h,O),E===null?k=K:E.sibling=K,E=K);return e&&C.forEach(function(be){return t(v,be)}),B&&Mt(v,O),k}function w(v,h,p,y){var k=Jn(p);if(typeof k!="function")throw Error(S(150));if(p=k.call(p),p==null)throw Error(S(151));for(var E=k=null,C=h,O=h=0,K=null,N=p.next();C!==null&&!N.done;O++,N=p.next()){C.index>O?(K=C,C=null):K=C.sibling;var be=f(v,C,N.value,y);if(be===null){C===null&&(C=K);break}e&&C&&be.alternate===null&&t(v,C),h=s(be,h,O),E===null?k=be:E.sibling=be,E=be,C=K}if(N.done)return n(v,C),B&&Mt(v,O),k;if(C===null){for(;!N.done;O++,N=p.next())N=d(v,N.value,y),N!==null&&(h=s(N,h,O),E===null?k=N:E.sibling=N,E=N);return B&&Mt(v,O),k}for(C=r(v,C);!N.done;O++,N=p.next())N=g(C,v,O,N.value,y),N!==null&&(e&&N.alternate!==null&&C.delete(N.key===null?O:N.key),h=s(N,h,O),E===null?k=N:E.sibling=N,E=N);return e&&C.forEach(function(qn){return t(v,qn)}),B&&Mt(v,O),k}function _(v,h,p,y){if(typeof p=="object"&&p!==null&&p.type===dn&&p.key===null&&(p=p.props.children),typeof p=="object"&&p!==null){switch(p.$$typeof){case Zr:e:{for(var k=p.key,E=h;E!==null;){if(E.key===k){if(k=p.type,k===dn){if(E.tag===7){n(v,E.sibling),h=i(E,p.props.children),h.return=v,v=h;break e}}else if(E.elementType===k||typeof k=="object"&&k!==null&&k.$$typeof===dt&&cu(k)===E.type){n(v,E.sibling),h=i(E,p.props),h.ref=Zn(v,E,p),h.return=v,v=h;break e}n(v,E);break}else t(v,E);E=E.sibling}p.type===dn?(h=Jt(p.props.children,v.mode,y,p.key),h.return=v,v=h):(y=$i(p.type,p.key,p.props,null,v.mode,y),y.ref=Zn(v,h,p),y.return=v,v=y)}return o(v);case cn:e:{for(E=p.key;h!==null;){if(h.key===E)if(h.tag===4&&h.stateNode.containerInfo===p.containerInfo&&h.stateNode.implementation===p.implementation){n(v,h.sibling),h=i(h,p.children||[]),h.return=v,v=h;break e}else{n(v,h);break}else t(v,h);h=h.sibling}h=lo(p,v.mode,y),h.return=v,v=h}return o(v);case dt:return E=p._init,_(v,h,E(p._payload),y)}if(ir(p))return m(v,h,p,y);if(Jn(p))return w(v,h,p,y);ci(v,p)}return typeof p=="string"&&p!==""||typeof p=="number"?(p=""+p,h!==null&&h.tag===6?(n(v,h.sibling),h=i(h,p),h.return=v,v=h):(n(v,h),h=oo(p,v.mode,y),h.return=v,v=h),o(v)):n(v,h)}return _}var $n=Ed(!0),xd=Ed(!1),qi=$t(null),Ki=null,wn=null,Hl=null;function ql(){Hl=wn=Ki=null}function Kl(e){var t=qi.current;F(qi),e._currentValue=t}function Ko(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Tn(e,t){Ki=e,Hl=wn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(we=!0),e.firstContext=null)}function Ae(e){var t=e._currentValue;if(Hl!==e)if(e={context:e,memoizedValue:t,next:null},wn===null){if(Ki===null)throw Error(S(308));wn=e,Ki.dependencies={lanes:0,firstContext:e}}else wn=wn.next=e;return t}var Ht=null;function Jl(e){Ht===null?Ht=[e]:Ht.push(e)}function Cd(e,t,n,r){var i=t.interleaved;return i===null?(n.next=n,Jl(t)):(n.next=i.next,i.next=n),t.interleaved=n,ot(e,r)}function ot(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var ft=!1;function Ql(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Pd(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function rt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function xt(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,b&2){var i=r.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),r.pending=t,ot(e,n)}return i=r.interleaved,i===null?(t.next=t,Jl(r)):(t.next=i.next,i.next=t),r.interleaved=t,ot(e,n)}function Pi(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Al(e,n)}}function du(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var i=null,s=null;if(n=n.firstBaseUpdate,n!==null){do{var o={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};s===null?i=s=o:s=s.next=o,n=n.next}while(n!==null);s===null?i=s=t:s=s.next=t}else i=s=t;n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:s,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Ji(e,t,n,r){var i=e.updateQueue;ft=!1;var s=i.firstBaseUpdate,o=i.lastBaseUpdate,l=i.shared.pending;if(l!==null){i.shared.pending=null;var a=l,u=a.next;a.next=null,o===null?s=u:o.next=u,o=a;var c=e.alternate;c!==null&&(c=c.updateQueue,l=c.lastBaseUpdate,l!==o&&(l===null?c.firstBaseUpdate=u:l.next=u,c.lastBaseUpdate=a))}if(s!==null){var d=i.baseState;o=0,c=u=a=null,l=s;do{var f=l.lane,g=l.eventTime;if((r&f)===f){c!==null&&(c=c.next={eventTime:g,lane:0,tag:l.tag,payload:l.payload,callback:l.callback,next:null});e:{var m=e,w=l;switch(f=t,g=n,w.tag){case 1:if(m=w.payload,typeof m=="function"){d=m.call(g,d,f);break e}d=m;break e;case 3:m.flags=m.flags&-65537|128;case 0:if(m=w.payload,f=typeof m=="function"?m.call(g,d,f):m,f==null)break e;d=H({},d,f);break e;case 2:ft=!0}}l.callback!==null&&l.lane!==0&&(e.flags|=64,f=i.effects,f===null?i.effects=[l]:f.push(l))}else g={eventTime:g,lane:f,tag:l.tag,payload:l.payload,callback:l.callback,next:null},c===null?(u=c=g,a=d):c=c.next=g,o|=f;if(l=l.next,l===null){if(l=i.shared.pending,l===null)break;f=l,l=f.next,f.next=null,i.lastBaseUpdate=f,i.shared.pending=null}}while(1);if(c===null&&(a=d),i.baseState=a,i.firstBaseUpdate=u,i.lastBaseUpdate=c,t=i.shared.interleaved,t!==null){i=t;do o|=i.lane,i=i.next;while(i!==t)}else s===null&&(i.shared.lanes=0);Xt|=o,e.lanes=o,e.memoizedState=d}}function fu(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],i=r.callback;if(i!==null){if(r.callback=null,r=n,typeof i!="function")throw Error(S(191,i));i.call(r)}}}var qr={},Ge=$t(qr),Lr=$t(qr),Ar=$t(qr);function qt(e){if(e===qr)throw Error(S(174));return e}function Gl(e,t){switch(z(Ar,t),z(Lr,e),z(Ge,qr),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:To(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=To(t,e)}F(Ge),z(Ge,t)}function Ln(){F(Ge),F(Lr),F(Ar)}function Td(e){qt(Ar.current);var t=qt(Ge.current),n=To(t,e.type);t!==n&&(z(Lr,e),z(Ge,n))}function Yl(e){Lr.current===e&&(F(Ge),F(Lr))}var W=$t(0);function Qi(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var eo=[];function Xl(){for(var e=0;e<eo.length;e++)eo[e]._workInProgressVersionPrimary=null;eo.length=0}var Ti=at.ReactCurrentDispatcher,to=at.ReactCurrentBatchConfig,Yt=0,V=null,X=null,ne=null,Gi=!1,pr=!1,Nr=0,ov=0;function le(){throw Error(S(321))}function Zl(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!He(e[n],t[n]))return!1;return!0}function ea(e,t,n,r,i,s){if(Yt=s,V=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Ti.current=e===null||e.memoizedState===null?cv:dv,e=n(r,i),pr){s=0;do{if(pr=!1,Nr=0,25<=s)throw Error(S(301));s+=1,ne=X=null,t.updateQueue=null,Ti.current=fv,e=n(r,i)}while(pr)}if(Ti.current=Yi,t=X!==null&&X.next!==null,Yt=0,ne=X=V=null,Gi=!1,t)throw Error(S(300));return e}function ta(){var e=Nr!==0;return Nr=0,e}function Ke(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ne===null?V.memoizedState=ne=e:ne=ne.next=e,ne}function Ne(){if(X===null){var e=V.alternate;e=e!==null?e.memoizedState:null}else e=X.next;var t=ne===null?V.memoizedState:ne.next;if(t!==null)ne=t,X=e;else{if(e===null)throw Error(S(310));X=e,e={memoizedState:X.memoizedState,baseState:X.baseState,baseQueue:X.baseQueue,queue:X.queue,next:null},ne===null?V.memoizedState=ne=e:ne=ne.next=e}return ne}function br(e,t){return typeof t=="function"?t(e):t}function no(e){var t=Ne(),n=t.queue;if(n===null)throw Error(S(311));n.lastRenderedReducer=e;var r=X,i=r.baseQueue,s=n.pending;if(s!==null){if(i!==null){var o=i.next;i.next=s.next,s.next=o}r.baseQueue=i=s,n.pending=null}if(i!==null){s=i.next,r=r.baseState;var l=o=null,a=null,u=s;do{var c=u.lane;if((Yt&c)===c)a!==null&&(a=a.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var d={lane:c,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};a===null?(l=a=d,o=r):a=a.next=d,V.lanes|=c,Xt|=c}u=u.next}while(u!==null&&u!==s);a===null?o=r:a.next=l,He(r,t.memoizedState)||(we=!0),t.memoizedState=r,t.baseState=o,t.baseQueue=a,n.lastRenderedState=r}if(e=n.interleaved,e!==null){i=e;do s=i.lane,V.lanes|=s,Xt|=s,i=i.next;while(i!==e)}else i===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function ro(e){var t=Ne(),n=t.queue;if(n===null)throw Error(S(311));n.lastRenderedReducer=e;var r=n.dispatch,i=n.pending,s=t.memoizedState;if(i!==null){n.pending=null;var o=i=i.next;do s=e(s,o.action),o=o.next;while(o!==i);He(s,t.memoizedState)||(we=!0),t.memoizedState=s,t.baseQueue===null&&(t.baseState=s),n.lastRenderedState=s}return[s,r]}function jd(){}function Od(e,t){var n=V,r=Ne(),i=t(),s=!He(r.memoizedState,i);if(s&&(r.memoizedState=i,we=!0),r=r.queue,na($d.bind(null,n,r,e),[e]),r.getSnapshot!==t||s||ne!==null&&ne.memoizedState.tag&1){if(n.flags|=2048,Ur(9,Id.bind(null,n,r,i,t),void 0,null),re===null)throw Error(S(349));Yt&30||Rd(n,t,i)}return i}function Rd(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=V.updateQueue,t===null?(t={lastEffect:null,stores:null},V.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Id(e,t,n,r){t.value=n,t.getSnapshot=r,Ld(t)&&Ad(e)}function $d(e,t,n){return n(function(){Ld(t)&&Ad(e)})}function Ld(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!He(e,n)}catch{return!0}}function Ad(e){var t=ot(e,1);t!==null&&Ve(t,e,1,-1)}function hu(e){var t=Ke();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:br,lastRenderedState:e},t.queue=e,e=e.dispatch=uv.bind(null,V,e),[t.memoizedState,e]}function Ur(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=V.updateQueue,t===null?(t={lastEffect:null,stores:null},V.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Nd(){return Ne().memoizedState}function ji(e,t,n,r){var i=Ke();V.flags|=e,i.memoizedState=Ur(1|t,n,void 0,r===void 0?null:r)}function ps(e,t,n,r){var i=Ne();r=r===void 0?null:r;var s=void 0;if(X!==null){var o=X.memoizedState;if(s=o.destroy,r!==null&&Zl(r,o.deps)){i.memoizedState=Ur(t,n,s,r);return}}V.flags|=e,i.memoizedState=Ur(1|t,n,s,r)}function pu(e,t){return ji(8390656,8,e,t)}function na(e,t){return ps(2048,8,e,t)}function bd(e,t){return ps(4,2,e,t)}function Ud(e,t){return ps(4,4,e,t)}function Dd(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function zd(e,t,n){return n=n!=null?n.concat([e]):null,ps(4,4,Dd.bind(null,t,e),n)}function ra(){}function Md(e,t){var n=Ne();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Zl(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Fd(e,t){var n=Ne();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Zl(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Bd(e,t,n){return Yt&21?(He(n,t)||(n=Kc(),V.lanes|=n,Xt|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,we=!0),e.memoizedState=n)}function lv(e,t){var n=U;U=n!==0&&4>n?n:4,e(!0);var r=to.transition;to.transition={};try{e(!1),t()}finally{U=n,to.transition=r}}function Wd(){return Ne().memoizedState}function av(e,t,n){var r=Pt(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Vd(e))Hd(t,n);else if(n=Cd(e,t,n,r),n!==null){var i=he();Ve(n,e,r,i),qd(n,t,r)}}function uv(e,t,n){var r=Pt(e),i={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Vd(e))Hd(t,i);else{var s=e.alternate;if(e.lanes===0&&(s===null||s.lanes===0)&&(s=t.lastRenderedReducer,s!==null))try{var o=t.lastRenderedState,l=s(o,n);if(i.hasEagerState=!0,i.eagerState=l,He(l,o)){var a=t.interleaved;a===null?(i.next=i,Jl(t)):(i.next=a.next,a.next=i),t.interleaved=i;return}}catch{}finally{}n=Cd(e,t,i,r),n!==null&&(i=he(),Ve(n,e,r,i),qd(n,t,r))}}function Vd(e){var t=e.alternate;return e===V||t!==null&&t===V}function Hd(e,t){pr=Gi=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function qd(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Al(e,n)}}var Yi={readContext:Ae,useCallback:le,useContext:le,useEffect:le,useImperativeHandle:le,useInsertionEffect:le,useLayoutEffect:le,useMemo:le,useReducer:le,useRef:le,useState:le,useDebugValue:le,useDeferredValue:le,useTransition:le,useMutableSource:le,useSyncExternalStore:le,useId:le,unstable_isNewReconciler:!1},cv={readContext:Ae,useCallback:function(e,t){return Ke().memoizedState=[e,t===void 0?null:t],e},useContext:Ae,useEffect:pu,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,ji(4194308,4,Dd.bind(null,t,e),n)},useLayoutEffect:function(e,t){return ji(4194308,4,e,t)},useInsertionEffect:function(e,t){return ji(4,2,e,t)},useMemo:function(e,t){var n=Ke();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Ke();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=av.bind(null,V,e),[r.memoizedState,e]},useRef:function(e){var t=Ke();return e={current:e},t.memoizedState=e},useState:hu,useDebugValue:ra,useDeferredValue:function(e){return Ke().memoizedState=e},useTransition:function(){var e=hu(!1),t=e[0];return e=lv.bind(null,e[1]),Ke().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=V,i=Ke();if(B){if(n===void 0)throw Error(S(407));n=n()}else{if(n=t(),re===null)throw Error(S(349));Yt&30||Rd(r,t,n)}i.memoizedState=n;var s={value:n,getSnapshot:t};return i.queue=s,pu($d.bind(null,r,s,e),[e]),r.flags|=2048,Ur(9,Id.bind(null,r,s,n,t),void 0,null),n},useId:function(){var e=Ke(),t=re.identifierPrefix;if(B){var n=nt,r=tt;n=(r&~(1<<32-We(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=Nr++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=ov++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},dv={readContext:Ae,useCallback:Md,useContext:Ae,useEffect:na,useImperativeHandle:zd,useInsertionEffect:bd,useLayoutEffect:Ud,useMemo:Fd,useReducer:no,useRef:Nd,useState:function(){return no(br)},useDebugValue:ra,useDeferredValue:function(e){var t=Ne();return Bd(t,X.memoizedState,e)},useTransition:function(){var e=no(br)[0],t=Ne().memoizedState;return[e,t]},useMutableSource:jd,useSyncExternalStore:Od,useId:Wd,unstable_isNewReconciler:!1},fv={readContext:Ae,useCallback:Md,useContext:Ae,useEffect:na,useImperativeHandle:zd,useInsertionEffect:bd,useLayoutEffect:Ud,useMemo:Fd,useReducer:ro,useRef:Nd,useState:function(){return ro(br)},useDebugValue:ra,useDeferredValue:function(e){var t=Ne();return X===null?t.memoizedState=e:Bd(t,X.memoizedState,e)},useTransition:function(){var e=ro(br)[0],t=Ne().memoizedState;return[e,t]},useMutableSource:jd,useSyncExternalStore:Od,useId:Wd,unstable_isNewReconciler:!1};function ze(e,t){if(e&&e.defaultProps){t=H({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Jo(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:H({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var vs={isMounted:function(e){return(e=e._reactInternals)?tn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=he(),i=Pt(e),s=rt(r,i);s.payload=t,n!=null&&(s.callback=n),t=xt(e,s,i),t!==null&&(Ve(t,e,i,r),Pi(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=he(),i=Pt(e),s=rt(r,i);s.tag=1,s.payload=t,n!=null&&(s.callback=n),t=xt(e,s,i),t!==null&&(Ve(t,e,i,r),Pi(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=he(),r=Pt(e),i=rt(n,r);i.tag=2,t!=null&&(i.callback=t),t=xt(e,i,r),t!==null&&(Ve(t,e,r,n),Pi(t,e,r))}};function vu(e,t,n,r,i,s,o){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,s,o):t.prototype&&t.prototype.isPureReactComponent?!Or(n,r)||!Or(i,s):!0}function Kd(e,t,n){var r=!1,i=Rt,s=t.contextType;return typeof s=="object"&&s!==null?s=Ae(s):(i=Se(t)?Qt:de.current,r=t.contextTypes,s=(r=r!=null)?Rn(e,i):Rt),t=new t(n,s),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=vs,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=s),t}function gu(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&vs.enqueueReplaceState(t,t.state,null)}function Qo(e,t,n,r){var i=e.stateNode;i.props=n,i.state=e.memoizedState,i.refs={},Ql(e);var s=t.contextType;typeof s=="object"&&s!==null?i.context=Ae(s):(s=Se(t)?Qt:de.current,i.context=Rn(e,s)),i.state=e.memoizedState,s=t.getDerivedStateFromProps,typeof s=="function"&&(Jo(e,t,s,n),i.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(t=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),t!==i.state&&vs.enqueueReplaceState(i,i.state,null),Ji(e,n,i,r),i.state=e.memoizedState),typeof i.componentDidMount=="function"&&(e.flags|=4194308)}function An(e,t){try{var n="",r=t;do n+=Mh(r),r=r.return;while(r);var i=n}catch(s){i=`
Error generating stack: `+s.message+`
`+s.stack}return{value:e,source:t,stack:i,digest:null}}function io(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Go(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var hv=typeof WeakMap=="function"?WeakMap:Map;function Jd(e,t,n){n=rt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Zi||(Zi=!0,ol=r),Go(e,t)},n}function Qd(e,t,n){n=rt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var i=t.value;n.payload=function(){return r(i)},n.callback=function(){Go(e,t)}}var s=e.stateNode;return s!==null&&typeof s.componentDidCatch=="function"&&(n.callback=function(){Go(e,t),typeof r!="function"&&(Ct===null?Ct=new Set([this]):Ct.add(this));var o=t.stack;this.componentDidCatch(t.value,{componentStack:o!==null?o:""})}),n}function mu(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new hv;var i=new Set;r.set(t,i)}else i=r.get(t),i===void 0&&(i=new Set,r.set(t,i));i.has(n)||(i.add(n),e=Tv.bind(null,e,t,n),t.then(e,e))}function yu(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function wu(e,t,n,r,i){return e.mode&1?(e.flags|=65536,e.lanes=i,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=rt(-1,1),t.tag=2,xt(n,t,1))),n.lanes|=1),e)}var pv=at.ReactCurrentOwner,we=!1;function fe(e,t,n,r){t.child=e===null?xd(t,null,n,r):$n(t,e.child,n,r)}function _u(e,t,n,r,i){n=n.render;var s=t.ref;return Tn(t,i),r=ea(e,t,n,r,s,i),n=ta(),e!==null&&!we?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,lt(e,t,i)):(B&&n&&Bl(t),t.flags|=1,fe(e,t,r,i),t.child)}function Su(e,t,n,r,i){if(e===null){var s=n.type;return typeof s=="function"&&!da(s)&&s.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=s,Gd(e,t,s,r,i)):(e=$i(n.type,null,r,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(s=e.child,!(e.lanes&i)){var o=s.memoizedProps;if(n=n.compare,n=n!==null?n:Or,n(o,r)&&e.ref===t.ref)return lt(e,t,i)}return t.flags|=1,e=Tt(s,r),e.ref=t.ref,e.return=t,t.child=e}function Gd(e,t,n,r,i){if(e!==null){var s=e.memoizedProps;if(Or(s,r)&&e.ref===t.ref)if(we=!1,t.pendingProps=r=s,(e.lanes&i)!==0)e.flags&131072&&(we=!0);else return t.lanes=e.lanes,lt(e,t,i)}return Yo(e,t,n,r,i)}function Yd(e,t,n){var r=t.pendingProps,i=r.children,s=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},z(Sn,Ee),Ee|=n;else{if(!(n&1073741824))return e=s!==null?s.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,z(Sn,Ee),Ee|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=s!==null?s.baseLanes:n,z(Sn,Ee),Ee|=r}else s!==null?(r=s.baseLanes|n,t.memoizedState=null):r=n,z(Sn,Ee),Ee|=r;return fe(e,t,i,n),t.child}function Xd(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Yo(e,t,n,r,i){var s=Se(n)?Qt:de.current;return s=Rn(t,s),Tn(t,i),n=ea(e,t,n,r,s,i),r=ta(),e!==null&&!we?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,lt(e,t,i)):(B&&r&&Bl(t),t.flags|=1,fe(e,t,n,i),t.child)}function ku(e,t,n,r,i){if(Se(n)){var s=!0;Wi(t)}else s=!1;if(Tn(t,i),t.stateNode===null)Oi(e,t),Kd(t,n,r),Qo(t,n,r,i),r=!0;else if(e===null){var o=t.stateNode,l=t.memoizedProps;o.props=l;var a=o.context,u=n.contextType;typeof u=="object"&&u!==null?u=Ae(u):(u=Se(n)?Qt:de.current,u=Rn(t,u));var c=n.getDerivedStateFromProps,d=typeof c=="function"||typeof o.getSnapshotBeforeUpdate=="function";d||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(l!==r||a!==u)&&gu(t,o,r,u),ft=!1;var f=t.memoizedState;o.state=f,Ji(t,r,o,i),a=t.memoizedState,l!==r||f!==a||_e.current||ft?(typeof c=="function"&&(Jo(t,n,c,r),a=t.memoizedState),(l=ft||vu(t,n,l,r,f,a,u))?(d||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount()),typeof o.componentDidMount=="function"&&(t.flags|=4194308)):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=a),o.props=r,o.state=a,o.context=u,r=l):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{o=t.stateNode,Pd(e,t),l=t.memoizedProps,u=t.type===t.elementType?l:ze(t.type,l),o.props=u,d=t.pendingProps,f=o.context,a=n.contextType,typeof a=="object"&&a!==null?a=Ae(a):(a=Se(n)?Qt:de.current,a=Rn(t,a));var g=n.getDerivedStateFromProps;(c=typeof g=="function"||typeof o.getSnapshotBeforeUpdate=="function")||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(l!==d||f!==a)&&gu(t,o,r,a),ft=!1,f=t.memoizedState,o.state=f,Ji(t,r,o,i);var m=t.memoizedState;l!==d||f!==m||_e.current||ft?(typeof g=="function"&&(Jo(t,n,g,r),m=t.memoizedState),(u=ft||vu(t,n,u,r,f,m,a)||!1)?(c||typeof o.UNSAFE_componentWillUpdate!="function"&&typeof o.componentWillUpdate!="function"||(typeof o.componentWillUpdate=="function"&&o.componentWillUpdate(r,m,a),typeof o.UNSAFE_componentWillUpdate=="function"&&o.UNSAFE_componentWillUpdate(r,m,a)),typeof o.componentDidUpdate=="function"&&(t.flags|=4),typeof o.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof o.componentDidUpdate!="function"||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=m),o.props=r,o.state=m,o.context=a,r=u):(typeof o.componentDidUpdate!="function"||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return Xo(e,t,n,r,s,i)}function Xo(e,t,n,r,i,s){Xd(e,t);var o=(t.flags&128)!==0;if(!r&&!o)return i&&lu(t,n,!1),lt(e,t,s);r=t.stateNode,pv.current=t;var l=o&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&o?(t.child=$n(t,e.child,null,s),t.child=$n(t,null,l,s)):fe(e,t,l,s),t.memoizedState=r.state,i&&lu(t,n,!0),t.child}function Zd(e){var t=e.stateNode;t.pendingContext?ou(e,t.pendingContext,t.pendingContext!==t.context):t.context&&ou(e,t.context,!1),Gl(e,t.containerInfo)}function Eu(e,t,n,r,i){return In(),Vl(i),t.flags|=256,fe(e,t,n,r),t.child}var Zo={dehydrated:null,treeContext:null,retryLane:0};function el(e){return{baseLanes:e,cachePool:null,transitions:null}}function ef(e,t,n){var r=t.pendingProps,i=W.current,s=!1,o=(t.flags&128)!==0,l;if((l=o)||(l=e!==null&&e.memoizedState===null?!1:(i&2)!==0),l?(s=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(i|=1),z(W,i&1),e===null)return qo(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(o=r.children,e=r.fallback,s?(r=t.mode,s=t.child,o={mode:"hidden",children:o},!(r&1)&&s!==null?(s.childLanes=0,s.pendingProps=o):s=ys(o,r,0,null),e=Jt(e,r,n,null),s.return=t,e.return=t,s.sibling=e,t.child=s,t.child.memoizedState=el(n),t.memoizedState=Zo,e):ia(t,o));if(i=e.memoizedState,i!==null&&(l=i.dehydrated,l!==null))return vv(e,t,o,r,l,i,n);if(s){s=r.fallback,o=t.mode,i=e.child,l=i.sibling;var a={mode:"hidden",children:r.children};return!(o&1)&&t.child!==i?(r=t.child,r.childLanes=0,r.pendingProps=a,t.deletions=null):(r=Tt(i,a),r.subtreeFlags=i.subtreeFlags&14680064),l!==null?s=Tt(l,s):(s=Jt(s,o,n,null),s.flags|=2),s.return=t,r.return=t,r.sibling=s,t.child=r,r=s,s=t.child,o=e.child.memoizedState,o=o===null?el(n):{baseLanes:o.baseLanes|n,cachePool:null,transitions:o.transitions},s.memoizedState=o,s.childLanes=e.childLanes&~n,t.memoizedState=Zo,r}return s=e.child,e=s.sibling,r=Tt(s,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function ia(e,t){return t=ys({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function di(e,t,n,r){return r!==null&&Vl(r),$n(t,e.child,null,n),e=ia(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function vv(e,t,n,r,i,s,o){if(n)return t.flags&256?(t.flags&=-257,r=io(Error(S(422))),di(e,t,o,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(s=r.fallback,i=t.mode,r=ys({mode:"visible",children:r.children},i,0,null),s=Jt(s,i,o,null),s.flags|=2,r.return=t,s.return=t,r.sibling=s,t.child=r,t.mode&1&&$n(t,e.child,null,o),t.child.memoizedState=el(o),t.memoizedState=Zo,s);if(!(t.mode&1))return di(e,t,o,null);if(i.data==="$!"){if(r=i.nextSibling&&i.nextSibling.dataset,r)var l=r.dgst;return r=l,s=Error(S(419)),r=io(s,r,void 0),di(e,t,o,r)}if(l=(o&e.childLanes)!==0,we||l){if(r=re,r!==null){switch(o&-o){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}i=i&(r.suspendedLanes|o)?0:i,i!==0&&i!==s.retryLane&&(s.retryLane=i,ot(e,i),Ve(r,e,i,-1))}return ca(),r=io(Error(S(421))),di(e,t,o,r)}return i.data==="$?"?(t.flags|=128,t.child=e.child,t=jv.bind(null,e),i._reactRetry=t,null):(e=s.treeContext,xe=Et(i.nextSibling),Ce=t,B=!0,Be=null,e!==null&&(Oe[Re++]=tt,Oe[Re++]=nt,Oe[Re++]=Gt,tt=e.id,nt=e.overflow,Gt=t),t=ia(t,r.children),t.flags|=4096,t)}function xu(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Ko(e.return,t,n)}function so(e,t,n,r,i){var s=e.memoizedState;s===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(s.isBackwards=t,s.rendering=null,s.renderingStartTime=0,s.last=r,s.tail=n,s.tailMode=i)}function tf(e,t,n){var r=t.pendingProps,i=r.revealOrder,s=r.tail;if(fe(e,t,r.children,n),r=W.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&xu(e,n,t);else if(e.tag===19)xu(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(z(W,r),!(t.mode&1))t.memoizedState=null;else switch(i){case"forwards":for(n=t.child,i=null;n!==null;)e=n.alternate,e!==null&&Qi(e)===null&&(i=n),n=n.sibling;n=i,n===null?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),so(t,!1,i,n,s);break;case"backwards":for(n=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&Qi(e)===null){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}so(t,!0,n,null,s);break;case"together":so(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Oi(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function lt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Xt|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(S(153));if(t.child!==null){for(e=t.child,n=Tt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Tt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function gv(e,t,n){switch(t.tag){case 3:Zd(t),In();break;case 5:Td(t);break;case 1:Se(t.type)&&Wi(t);break;case 4:Gl(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,i=t.memoizedProps.value;z(qi,r._currentValue),r._currentValue=i;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(z(W,W.current&1),t.flags|=128,null):n&t.child.childLanes?ef(e,t,n):(z(W,W.current&1),e=lt(e,t,n),e!==null?e.sibling:null);z(W,W.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return tf(e,t,n);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),z(W,W.current),r)break;return null;case 22:case 23:return t.lanes=0,Yd(e,t,n)}return lt(e,t,n)}var nf,tl,rf,sf;nf=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};tl=function(){};rf=function(e,t,n,r){var i=e.memoizedProps;if(i!==r){e=t.stateNode,qt(Ge.current);var s=null;switch(n){case"input":i=Eo(e,i),r=Eo(e,r),s=[];break;case"select":i=H({},i,{value:void 0}),r=H({},r,{value:void 0}),s=[];break;case"textarea":i=Po(e,i),r=Po(e,r),s=[];break;default:typeof i.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=Fi)}jo(n,r);var o;n=null;for(u in i)if(!r.hasOwnProperty(u)&&i.hasOwnProperty(u)&&i[u]!=null)if(u==="style"){var l=i[u];for(o in l)l.hasOwnProperty(o)&&(n||(n={}),n[o]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(kr.hasOwnProperty(u)?s||(s=[]):(s=s||[]).push(u,null));for(u in r){var a=r[u];if(l=i!=null?i[u]:void 0,r.hasOwnProperty(u)&&a!==l&&(a!=null||l!=null))if(u==="style")if(l){for(o in l)!l.hasOwnProperty(o)||a&&a.hasOwnProperty(o)||(n||(n={}),n[o]="");for(o in a)a.hasOwnProperty(o)&&l[o]!==a[o]&&(n||(n={}),n[o]=a[o])}else n||(s||(s=[]),s.push(u,n)),n=a;else u==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,l=l?l.__html:void 0,a!=null&&l!==a&&(s=s||[]).push(u,a)):u==="children"?typeof a!="string"&&typeof a!="number"||(s=s||[]).push(u,""+a):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(kr.hasOwnProperty(u)?(a!=null&&u==="onScroll"&&M("scroll",e),s||l===a||(s=[])):(s=s||[]).push(u,a))}n&&(s=s||[]).push("style",n);var u=s;(t.updateQueue=u)&&(t.flags|=4)}};sf=function(e,t,n,r){n!==r&&(t.flags|=4)};function er(e,t){if(!B)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function ae(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags&14680064,r|=i.flags&14680064,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function mv(e,t,n){var r=t.pendingProps;switch(Wl(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return ae(t),null;case 1:return Se(t.type)&&Bi(),ae(t),null;case 3:return r=t.stateNode,Ln(),F(_e),F(de),Xl(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(ui(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Be!==null&&(ul(Be),Be=null))),tl(e,t),ae(t),null;case 5:Yl(t);var i=qt(Ar.current);if(n=t.type,e!==null&&t.stateNode!=null)rf(e,t,n,r,i),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(S(166));return ae(t),null}if(e=qt(Ge.current),ui(t)){r=t.stateNode,n=t.type;var s=t.memoizedProps;switch(r[Je]=t,r[$r]=s,e=(t.mode&1)!==0,n){case"dialog":M("cancel",r),M("close",r);break;case"iframe":case"object":case"embed":M("load",r);break;case"video":case"audio":for(i=0;i<or.length;i++)M(or[i],r);break;case"source":M("error",r);break;case"img":case"image":case"link":M("error",r),M("load",r);break;case"details":M("toggle",r);break;case"input":La(r,s),M("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!s.multiple},M("invalid",r);break;case"textarea":Na(r,s),M("invalid",r)}jo(n,s),i=null;for(var o in s)if(s.hasOwnProperty(o)){var l=s[o];o==="children"?typeof l=="string"?r.textContent!==l&&(s.suppressHydrationWarning!==!0&&ai(r.textContent,l,e),i=["children",l]):typeof l=="number"&&r.textContent!==""+l&&(s.suppressHydrationWarning!==!0&&ai(r.textContent,l,e),i=["children",""+l]):kr.hasOwnProperty(o)&&l!=null&&o==="onScroll"&&M("scroll",r)}switch(n){case"input":ei(r),Aa(r,s,!0);break;case"textarea":ei(r),ba(r);break;case"select":case"option":break;default:typeof s.onClick=="function"&&(r.onclick=Fi)}r=i,t.updateQueue=r,r!==null&&(t.flags|=4)}else{o=i.nodeType===9?i:i.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=$c(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=o.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=o.createElement(n,{is:r.is}):(e=o.createElement(n),n==="select"&&(o=e,r.multiple?o.multiple=!0:r.size&&(o.size=r.size))):e=o.createElementNS(e,n),e[Je]=t,e[$r]=r,nf(e,t,!1,!1),t.stateNode=e;e:{switch(o=Oo(n,r),n){case"dialog":M("cancel",e),M("close",e),i=r;break;case"iframe":case"object":case"embed":M("load",e),i=r;break;case"video":case"audio":for(i=0;i<or.length;i++)M(or[i],e);i=r;break;case"source":M("error",e),i=r;break;case"img":case"image":case"link":M("error",e),M("load",e),i=r;break;case"details":M("toggle",e),i=r;break;case"input":La(e,r),i=Eo(e,r),M("invalid",e);break;case"option":i=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},i=H({},r,{value:void 0}),M("invalid",e);break;case"textarea":Na(e,r),i=Po(e,r),M("invalid",e);break;default:i=r}jo(n,i),l=i;for(s in l)if(l.hasOwnProperty(s)){var a=l[s];s==="style"?Nc(e,a):s==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,a!=null&&Lc(e,a)):s==="children"?typeof a=="string"?(n!=="textarea"||a!=="")&&Er(e,a):typeof a=="number"&&Er(e,""+a):s!=="suppressContentEditableWarning"&&s!=="suppressHydrationWarning"&&s!=="autoFocus"&&(kr.hasOwnProperty(s)?a!=null&&s==="onScroll"&&M("scroll",e):a!=null&&jl(e,s,a,o))}switch(n){case"input":ei(e),Aa(e,r,!1);break;case"textarea":ei(e),ba(e);break;case"option":r.value!=null&&e.setAttribute("value",""+Ot(r.value));break;case"select":e.multiple=!!r.multiple,s=r.value,s!=null?En(e,!!r.multiple,s,!1):r.defaultValue!=null&&En(e,!!r.multiple,r.defaultValue,!0);break;default:typeof i.onClick=="function"&&(e.onclick=Fi)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return ae(t),null;case 6:if(e&&t.stateNode!=null)sf(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(S(166));if(n=qt(Ar.current),qt(Ge.current),ui(t)){if(r=t.stateNode,n=t.memoizedProps,r[Je]=t,(s=r.nodeValue!==n)&&(e=Ce,e!==null))switch(e.tag){case 3:ai(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&ai(r.nodeValue,n,(e.mode&1)!==0)}s&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[Je]=t,t.stateNode=r}return ae(t),null;case 13:if(F(W),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(B&&xe!==null&&t.mode&1&&!(t.flags&128))kd(),In(),t.flags|=98560,s=!1;else if(s=ui(t),r!==null&&r.dehydrated!==null){if(e===null){if(!s)throw Error(S(318));if(s=t.memoizedState,s=s!==null?s.dehydrated:null,!s)throw Error(S(317));s[Je]=t}else In(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;ae(t),s=!1}else Be!==null&&(ul(Be),Be=null),s=!0;if(!s)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||W.current&1?Z===0&&(Z=3):ca())),t.updateQueue!==null&&(t.flags|=4),ae(t),null);case 4:return Ln(),tl(e,t),e===null&&Rr(t.stateNode.containerInfo),ae(t),null;case 10:return Kl(t.type._context),ae(t),null;case 17:return Se(t.type)&&Bi(),ae(t),null;case 19:if(F(W),s=t.memoizedState,s===null)return ae(t),null;if(r=(t.flags&128)!==0,o=s.rendering,o===null)if(r)er(s,!1);else{if(Z!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(o=Qi(e),o!==null){for(t.flags|=128,er(s,!1),r=o.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)s=n,e=r,s.flags&=14680066,o=s.alternate,o===null?(s.childLanes=0,s.lanes=e,s.child=null,s.subtreeFlags=0,s.memoizedProps=null,s.memoizedState=null,s.updateQueue=null,s.dependencies=null,s.stateNode=null):(s.childLanes=o.childLanes,s.lanes=o.lanes,s.child=o.child,s.subtreeFlags=0,s.deletions=null,s.memoizedProps=o.memoizedProps,s.memoizedState=o.memoizedState,s.updateQueue=o.updateQueue,s.type=o.type,e=o.dependencies,s.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return z(W,W.current&1|2),t.child}e=e.sibling}s.tail!==null&&Q()>Nn&&(t.flags|=128,r=!0,er(s,!1),t.lanes=4194304)}else{if(!r)if(e=Qi(o),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),er(s,!0),s.tail===null&&s.tailMode==="hidden"&&!o.alternate&&!B)return ae(t),null}else 2*Q()-s.renderingStartTime>Nn&&n!==1073741824&&(t.flags|=128,r=!0,er(s,!1),t.lanes=4194304);s.isBackwards?(o.sibling=t.child,t.child=o):(n=s.last,n!==null?n.sibling=o:t.child=o,s.last=o)}return s.tail!==null?(t=s.tail,s.rendering=t,s.tail=t.sibling,s.renderingStartTime=Q(),t.sibling=null,n=W.current,z(W,r?n&1|2:n&1),t):(ae(t),null);case 22:case 23:return ua(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Ee&1073741824&&(ae(t),t.subtreeFlags&6&&(t.flags|=8192)):ae(t),null;case 24:return null;case 25:return null}throw Error(S(156,t.tag))}function yv(e,t){switch(Wl(t),t.tag){case 1:return Se(t.type)&&Bi(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Ln(),F(_e),F(de),Xl(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Yl(t),null;case 13:if(F(W),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(S(340));In()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return F(W),null;case 4:return Ln(),null;case 10:return Kl(t.type._context),null;case 22:case 23:return ua(),null;case 24:return null;default:return null}}var fi=!1,ce=!1,wv=typeof WeakSet=="function"?WeakSet:Set,P=null;function _n(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){q(e,t,r)}else n.current=null}function nl(e,t,n){try{n()}catch(r){q(e,t,r)}}var Cu=!1;function _v(e,t){if(zo=Di,e=ud(),Fl(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var i=r.anchorOffset,s=r.focusNode;r=r.focusOffset;try{n.nodeType,s.nodeType}catch{n=null;break e}var o=0,l=-1,a=-1,u=0,c=0,d=e,f=null;t:for(;;){for(var g;d!==n||i!==0&&d.nodeType!==3||(l=o+i),d!==s||r!==0&&d.nodeType!==3||(a=o+r),d.nodeType===3&&(o+=d.nodeValue.length),(g=d.firstChild)!==null;)f=d,d=g;for(;;){if(d===e)break t;if(f===n&&++u===i&&(l=o),f===s&&++c===r&&(a=o),(g=d.nextSibling)!==null)break;d=f,f=d.parentNode}d=g}n=l===-1||a===-1?null:{start:l,end:a}}else n=null}n=n||{start:0,end:0}}else n=null;for(Mo={focusedElem:e,selectionRange:n},Di=!1,P=t;P!==null;)if(t=P,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,P=e;else for(;P!==null;){t=P;try{var m=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(m!==null){var w=m.memoizedProps,_=m.memoizedState,v=t.stateNode,h=v.getSnapshotBeforeUpdate(t.elementType===t.type?w:ze(t.type,w),_);v.__reactInternalSnapshotBeforeUpdate=h}break;case 3:var p=t.stateNode.containerInfo;p.nodeType===1?p.textContent="":p.nodeType===9&&p.documentElement&&p.removeChild(p.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(S(163))}}catch(y){q(t,t.return,y)}if(e=t.sibling,e!==null){e.return=t.return,P=e;break}P=t.return}return m=Cu,Cu=!1,m}function vr(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var i=r=r.next;do{if((i.tag&e)===e){var s=i.destroy;i.destroy=void 0,s!==void 0&&nl(t,n,s)}i=i.next}while(i!==r)}}function gs(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function rl(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function of(e){var t=e.alternate;t!==null&&(e.alternate=null,of(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Je],delete t[$r],delete t[Wo],delete t[nv],delete t[rv])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function lf(e){return e.tag===5||e.tag===3||e.tag===4}function Pu(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||lf(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function il(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Fi));else if(r!==4&&(e=e.child,e!==null))for(il(e,t,n),e=e.sibling;e!==null;)il(e,t,n),e=e.sibling}function sl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(sl(e,t,n),e=e.sibling;e!==null;)sl(e,t,n),e=e.sibling}var ie=null,Me=!1;function ut(e,t,n){for(n=n.child;n!==null;)af(e,t,n),n=n.sibling}function af(e,t,n){if(Qe&&typeof Qe.onCommitFiberUnmount=="function")try{Qe.onCommitFiberUnmount(as,n)}catch{}switch(n.tag){case 5:ce||_n(n,t);case 6:var r=ie,i=Me;ie=null,ut(e,t,n),ie=r,Me=i,ie!==null&&(Me?(e=ie,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):ie.removeChild(n.stateNode));break;case 18:ie!==null&&(Me?(e=ie,n=n.stateNode,e.nodeType===8?Xs(e.parentNode,n):e.nodeType===1&&Xs(e,n),Tr(e)):Xs(ie,n.stateNode));break;case 4:r=ie,i=Me,ie=n.stateNode.containerInfo,Me=!0,ut(e,t,n),ie=r,Me=i;break;case 0:case 11:case 14:case 15:if(!ce&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){i=r=r.next;do{var s=i,o=s.destroy;s=s.tag,o!==void 0&&(s&2||s&4)&&nl(n,t,o),i=i.next}while(i!==r)}ut(e,t,n);break;case 1:if(!ce&&(_n(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(l){q(n,t,l)}ut(e,t,n);break;case 21:ut(e,t,n);break;case 22:n.mode&1?(ce=(r=ce)||n.memoizedState!==null,ut(e,t,n),ce=r):ut(e,t,n);break;default:ut(e,t,n)}}function Tu(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new wv),t.forEach(function(r){var i=Ov.bind(null,e,r);n.has(r)||(n.add(r),r.then(i,i))})}}function Ue(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var i=n[r];try{var s=e,o=t,l=o;e:for(;l!==null;){switch(l.tag){case 5:ie=l.stateNode,Me=!1;break e;case 3:ie=l.stateNode.containerInfo,Me=!0;break e;case 4:ie=l.stateNode.containerInfo,Me=!0;break e}l=l.return}if(ie===null)throw Error(S(160));af(s,o,i),ie=null,Me=!1;var a=i.alternate;a!==null&&(a.return=null),i.return=null}catch(u){q(i,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)uf(t,e),t=t.sibling}function uf(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Ue(t,e),qe(e),r&4){try{vr(3,e,e.return),gs(3,e)}catch(w){q(e,e.return,w)}try{vr(5,e,e.return)}catch(w){q(e,e.return,w)}}break;case 1:Ue(t,e),qe(e),r&512&&n!==null&&_n(n,n.return);break;case 5:if(Ue(t,e),qe(e),r&512&&n!==null&&_n(n,n.return),e.flags&32){var i=e.stateNode;try{Er(i,"")}catch(w){q(e,e.return,w)}}if(r&4&&(i=e.stateNode,i!=null)){var s=e.memoizedProps,o=n!==null?n.memoizedProps:s,l=e.type,a=e.updateQueue;if(e.updateQueue=null,a!==null)try{l==="input"&&s.type==="radio"&&s.name!=null&&Rc(i,s),Oo(l,o);var u=Oo(l,s);for(o=0;o<a.length;o+=2){var c=a[o],d=a[o+1];c==="style"?Nc(i,d):c==="dangerouslySetInnerHTML"?Lc(i,d):c==="children"?Er(i,d):jl(i,c,d,u)}switch(l){case"input":xo(i,s);break;case"textarea":Ic(i,s);break;case"select":var f=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!s.multiple;var g=s.value;g!=null?En(i,!!s.multiple,g,!1):f!==!!s.multiple&&(s.defaultValue!=null?En(i,!!s.multiple,s.defaultValue,!0):En(i,!!s.multiple,s.multiple?[]:"",!1))}i[$r]=s}catch(w){q(e,e.return,w)}}break;case 6:if(Ue(t,e),qe(e),r&4){if(e.stateNode===null)throw Error(S(162));i=e.stateNode,s=e.memoizedProps;try{i.nodeValue=s}catch(w){q(e,e.return,w)}}break;case 3:if(Ue(t,e),qe(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Tr(t.containerInfo)}catch(w){q(e,e.return,w)}break;case 4:Ue(t,e),qe(e);break;case 13:Ue(t,e),qe(e),i=e.child,i.flags&8192&&(s=i.memoizedState!==null,i.stateNode.isHidden=s,!s||i.alternate!==null&&i.alternate.memoizedState!==null||(la=Q())),r&4&&Tu(e);break;case 22:if(c=n!==null&&n.memoizedState!==null,e.mode&1?(ce=(u=ce)||c,Ue(t,e),ce=u):Ue(t,e),qe(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!c&&e.mode&1)for(P=e,c=e.child;c!==null;){for(d=P=c;P!==null;){switch(f=P,g=f.child,f.tag){case 0:case 11:case 14:case 15:vr(4,f,f.return);break;case 1:_n(f,f.return);var m=f.stateNode;if(typeof m.componentWillUnmount=="function"){r=f,n=f.return;try{t=r,m.props=t.memoizedProps,m.state=t.memoizedState,m.componentWillUnmount()}catch(w){q(r,n,w)}}break;case 5:_n(f,f.return);break;case 22:if(f.memoizedState!==null){Ou(d);continue}}g!==null?(g.return=f,P=g):Ou(d)}c=c.sibling}e:for(c=null,d=e;;){if(d.tag===5){if(c===null){c=d;try{i=d.stateNode,u?(s=i.style,typeof s.setProperty=="function"?s.setProperty("display","none","important"):s.display="none"):(l=d.stateNode,a=d.memoizedProps.style,o=a!=null&&a.hasOwnProperty("display")?a.display:null,l.style.display=Ac("display",o))}catch(w){q(e,e.return,w)}}}else if(d.tag===6){if(c===null)try{d.stateNode.nodeValue=u?"":d.memoizedProps}catch(w){q(e,e.return,w)}}else if((d.tag!==22&&d.tag!==23||d.memoizedState===null||d===e)&&d.child!==null){d.child.return=d,d=d.child;continue}if(d===e)break e;for(;d.sibling===null;){if(d.return===null||d.return===e)break e;c===d&&(c=null),d=d.return}c===d&&(c=null),d.sibling.return=d.return,d=d.sibling}}break;case 19:Ue(t,e),qe(e),r&4&&Tu(e);break;case 21:break;default:Ue(t,e),qe(e)}}function qe(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(lf(n)){var r=n;break e}n=n.return}throw Error(S(160))}switch(r.tag){case 5:var i=r.stateNode;r.flags&32&&(Er(i,""),r.flags&=-33);var s=Pu(e);sl(e,s,i);break;case 3:case 4:var o=r.stateNode.containerInfo,l=Pu(e);il(e,l,o);break;default:throw Error(S(161))}}catch(a){q(e,e.return,a)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Sv(e,t,n){P=e,cf(e)}function cf(e,t,n){for(var r=(e.mode&1)!==0;P!==null;){var i=P,s=i.child;if(i.tag===22&&r){var o=i.memoizedState!==null||fi;if(!o){var l=i.alternate,a=l!==null&&l.memoizedState!==null||ce;l=fi;var u=ce;if(fi=o,(ce=a)&&!u)for(P=i;P!==null;)o=P,a=o.child,o.tag===22&&o.memoizedState!==null?Ru(i):a!==null?(a.return=o,P=a):Ru(i);for(;s!==null;)P=s,cf(s),s=s.sibling;P=i,fi=l,ce=u}ju(e)}else i.subtreeFlags&8772&&s!==null?(s.return=i,P=s):ju(e)}}function ju(e){for(;P!==null;){var t=P;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:ce||gs(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!ce)if(n===null)r.componentDidMount();else{var i=t.elementType===t.type?n.memoizedProps:ze(t.type,n.memoizedProps);r.componentDidUpdate(i,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var s=t.updateQueue;s!==null&&fu(t,s,r);break;case 3:var o=t.updateQueue;if(o!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}fu(t,o,n)}break;case 5:var l=t.stateNode;if(n===null&&t.flags&4){n=l;var a=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":a.autoFocus&&n.focus();break;case"img":a.src&&(n.src=a.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var c=u.memoizedState;if(c!==null){var d=c.dehydrated;d!==null&&Tr(d)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(S(163))}ce||t.flags&512&&rl(t)}catch(f){q(t,t.return,f)}}if(t===e){P=null;break}if(n=t.sibling,n!==null){n.return=t.return,P=n;break}P=t.return}}function Ou(e){for(;P!==null;){var t=P;if(t===e){P=null;break}var n=t.sibling;if(n!==null){n.return=t.return,P=n;break}P=t.return}}function Ru(e){for(;P!==null;){var t=P;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{gs(4,t)}catch(a){q(t,n,a)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var i=t.return;try{r.componentDidMount()}catch(a){q(t,i,a)}}var s=t.return;try{rl(t)}catch(a){q(t,s,a)}break;case 5:var o=t.return;try{rl(t)}catch(a){q(t,o,a)}}}catch(a){q(t,t.return,a)}if(t===e){P=null;break}var l=t.sibling;if(l!==null){l.return=t.return,P=l;break}P=t.return}}var kv=Math.ceil,Xi=at.ReactCurrentDispatcher,sa=at.ReactCurrentOwner,$e=at.ReactCurrentBatchConfig,b=0,re=null,Y=null,se=0,Ee=0,Sn=$t(0),Z=0,Dr=null,Xt=0,ms=0,oa=0,gr=null,me=null,la=0,Nn=1/0,Xe=null,Zi=!1,ol=null,Ct=null,hi=!1,yt=null,es=0,mr=0,ll=null,Ri=-1,Ii=0;function he(){return b&6?Q():Ri!==-1?Ri:Ri=Q()}function Pt(e){return e.mode&1?b&2&&se!==0?se&-se:sv.transition!==null?(Ii===0&&(Ii=Kc()),Ii):(e=U,e!==0||(e=window.event,e=e===void 0?16:ed(e.type)),e):1}function Ve(e,t,n,r){if(50<mr)throw mr=0,ll=null,Error(S(185));Wr(e,n,r),(!(b&2)||e!==re)&&(e===re&&(!(b&2)&&(ms|=n),Z===4&&vt(e,se)),ke(e,r),n===1&&b===0&&!(t.mode&1)&&(Nn=Q()+500,hs&&Lt()))}function ke(e,t){var n=e.callbackNode;sp(e,t);var r=Ui(e,e===re?se:0);if(r===0)n!==null&&za(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&za(n),t===1)e.tag===0?iv(Iu.bind(null,e)):wd(Iu.bind(null,e)),ev(function(){!(b&6)&&Lt()}),n=null;else{switch(Jc(r)){case 1:n=Ll;break;case 4:n=Hc;break;case 16:n=bi;break;case 536870912:n=qc;break;default:n=bi}n=yf(n,df.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function df(e,t){if(Ri=-1,Ii=0,b&6)throw Error(S(327));var n=e.callbackNode;if(jn()&&e.callbackNode!==n)return null;var r=Ui(e,e===re?se:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=ts(e,r);else{t=r;var i=b;b|=2;var s=hf();(re!==e||se!==t)&&(Xe=null,Nn=Q()+500,Kt(e,t));do try{Cv();break}catch(l){ff(e,l)}while(1);ql(),Xi.current=s,b=i,Y!==null?t=0:(re=null,se=0,t=Z)}if(t!==0){if(t===2&&(i=Ao(e),i!==0&&(r=i,t=al(e,i))),t===1)throw n=Dr,Kt(e,0),vt(e,r),ke(e,Q()),n;if(t===6)vt(e,r);else{if(i=e.current.alternate,!(r&30)&&!Ev(i)&&(t=ts(e,r),t===2&&(s=Ao(e),s!==0&&(r=s,t=al(e,s))),t===1))throw n=Dr,Kt(e,0),vt(e,r),ke(e,Q()),n;switch(e.finishedWork=i,e.finishedLanes=r,t){case 0:case 1:throw Error(S(345));case 2:Ft(e,me,Xe);break;case 3:if(vt(e,r),(r&130023424)===r&&(t=la+500-Q(),10<t)){if(Ui(e,0)!==0)break;if(i=e.suspendedLanes,(i&r)!==r){he(),e.pingedLanes|=e.suspendedLanes&i;break}e.timeoutHandle=Bo(Ft.bind(null,e,me,Xe),t);break}Ft(e,me,Xe);break;case 4:if(vt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,i=-1;0<r;){var o=31-We(r);s=1<<o,o=t[o],o>i&&(i=o),r&=~s}if(r=i,r=Q()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*kv(r/1960))-r,10<r){e.timeoutHandle=Bo(Ft.bind(null,e,me,Xe),r);break}Ft(e,me,Xe);break;case 5:Ft(e,me,Xe);break;default:throw Error(S(329))}}}return ke(e,Q()),e.callbackNode===n?df.bind(null,e):null}function al(e,t){var n=gr;return e.current.memoizedState.isDehydrated&&(Kt(e,t).flags|=256),e=ts(e,t),e!==2&&(t=me,me=n,t!==null&&ul(t)),e}function ul(e){me===null?me=e:me.push.apply(me,e)}function Ev(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var i=n[r],s=i.getSnapshot;i=i.value;try{if(!He(s(),i))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function vt(e,t){for(t&=~oa,t&=~ms,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-We(t),r=1<<n;e[n]=-1,t&=~r}}function Iu(e){if(b&6)throw Error(S(327));jn();var t=Ui(e,0);if(!(t&1))return ke(e,Q()),null;var n=ts(e,t);if(e.tag!==0&&n===2){var r=Ao(e);r!==0&&(t=r,n=al(e,r))}if(n===1)throw n=Dr,Kt(e,0),vt(e,t),ke(e,Q()),n;if(n===6)throw Error(S(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Ft(e,me,Xe),ke(e,Q()),null}function aa(e,t){var n=b;b|=1;try{return e(t)}finally{b=n,b===0&&(Nn=Q()+500,hs&&Lt())}}function Zt(e){yt!==null&&yt.tag===0&&!(b&6)&&jn();var t=b;b|=1;var n=$e.transition,r=U;try{if($e.transition=null,U=1,e)return e()}finally{U=r,$e.transition=n,b=t,!(b&6)&&Lt()}}function ua(){Ee=Sn.current,F(Sn)}function Kt(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,Zp(n)),Y!==null)for(n=Y.return;n!==null;){var r=n;switch(Wl(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Bi();break;case 3:Ln(),F(_e),F(de),Xl();break;case 5:Yl(r);break;case 4:Ln();break;case 13:F(W);break;case 19:F(W);break;case 10:Kl(r.type._context);break;case 22:case 23:ua()}n=n.return}if(re=e,Y=e=Tt(e.current,null),se=Ee=t,Z=0,Dr=null,oa=ms=Xt=0,me=gr=null,Ht!==null){for(t=0;t<Ht.length;t++)if(n=Ht[t],r=n.interleaved,r!==null){n.interleaved=null;var i=r.next,s=n.pending;if(s!==null){var o=s.next;s.next=i,r.next=o}n.pending=r}Ht=null}return e}function ff(e,t){do{var n=Y;try{if(ql(),Ti.current=Yi,Gi){for(var r=V.memoizedState;r!==null;){var i=r.queue;i!==null&&(i.pending=null),r=r.next}Gi=!1}if(Yt=0,ne=X=V=null,pr=!1,Nr=0,sa.current=null,n===null||n.return===null){Z=1,Dr=t,Y=null;break}e:{var s=e,o=n.return,l=n,a=t;if(t=se,l.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){var u=a,c=l,d=c.tag;if(!(c.mode&1)&&(d===0||d===11||d===15)){var f=c.alternate;f?(c.updateQueue=f.updateQueue,c.memoizedState=f.memoizedState,c.lanes=f.lanes):(c.updateQueue=null,c.memoizedState=null)}var g=yu(o);if(g!==null){g.flags&=-257,wu(g,o,l,s,t),g.mode&1&&mu(s,u,t),t=g,a=u;var m=t.updateQueue;if(m===null){var w=new Set;w.add(a),t.updateQueue=w}else m.add(a);break e}else{if(!(t&1)){mu(s,u,t),ca();break e}a=Error(S(426))}}else if(B&&l.mode&1){var _=yu(o);if(_!==null){!(_.flags&65536)&&(_.flags|=256),wu(_,o,l,s,t),Vl(An(a,l));break e}}s=a=An(a,l),Z!==4&&(Z=2),gr===null?gr=[s]:gr.push(s),s=o;do{switch(s.tag){case 3:s.flags|=65536,t&=-t,s.lanes|=t;var v=Jd(s,a,t);du(s,v);break e;case 1:l=a;var h=s.type,p=s.stateNode;if(!(s.flags&128)&&(typeof h.getDerivedStateFromError=="function"||p!==null&&typeof p.componentDidCatch=="function"&&(Ct===null||!Ct.has(p)))){s.flags|=65536,t&=-t,s.lanes|=t;var y=Qd(s,l,t);du(s,y);break e}}s=s.return}while(s!==null)}vf(n)}catch(k){t=k,Y===n&&n!==null&&(Y=n=n.return);continue}break}while(1)}function hf(){var e=Xi.current;return Xi.current=Yi,e===null?Yi:e}function ca(){(Z===0||Z===3||Z===2)&&(Z=4),re===null||!(Xt&268435455)&&!(ms&268435455)||vt(re,se)}function ts(e,t){var n=b;b|=2;var r=hf();(re!==e||se!==t)&&(Xe=null,Kt(e,t));do try{xv();break}catch(i){ff(e,i)}while(1);if(ql(),b=n,Xi.current=r,Y!==null)throw Error(S(261));return re=null,se=0,Z}function xv(){for(;Y!==null;)pf(Y)}function Cv(){for(;Y!==null&&!Gh();)pf(Y)}function pf(e){var t=mf(e.alternate,e,Ee);e.memoizedProps=e.pendingProps,t===null?vf(e):Y=t,sa.current=null}function vf(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=yv(n,t),n!==null){n.flags&=32767,Y=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{Z=6,Y=null;return}}else if(n=mv(n,t,Ee),n!==null){Y=n;return}if(t=t.sibling,t!==null){Y=t;return}Y=t=e}while(t!==null);Z===0&&(Z=5)}function Ft(e,t,n){var r=U,i=$e.transition;try{$e.transition=null,U=1,Pv(e,t,n,r)}finally{$e.transition=i,U=r}return null}function Pv(e,t,n,r){do jn();while(yt!==null);if(b&6)throw Error(S(327));n=e.finishedWork;var i=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(S(177));e.callbackNode=null,e.callbackPriority=0;var s=n.lanes|n.childLanes;if(op(e,s),e===re&&(Y=re=null,se=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||hi||(hi=!0,yf(bi,function(){return jn(),null})),s=(n.flags&15990)!==0,n.subtreeFlags&15990||s){s=$e.transition,$e.transition=null;var o=U;U=1;var l=b;b|=4,sa.current=null,_v(e,n),uf(n,e),qp(Mo),Di=!!zo,Mo=zo=null,e.current=n,Sv(n),Yh(),b=l,U=o,$e.transition=s}else e.current=n;if(hi&&(hi=!1,yt=e,es=i),s=e.pendingLanes,s===0&&(Ct=null),ep(n.stateNode),ke(e,Q()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)i=t[n],r(i.value,{componentStack:i.stack,digest:i.digest});if(Zi)throw Zi=!1,e=ol,ol=null,e;return es&1&&e.tag!==0&&jn(),s=e.pendingLanes,s&1?e===ll?mr++:(mr=0,ll=e):mr=0,Lt(),null}function jn(){if(yt!==null){var e=Jc(es),t=$e.transition,n=U;try{if($e.transition=null,U=16>e?16:e,yt===null)var r=!1;else{if(e=yt,yt=null,es=0,b&6)throw Error(S(331));var i=b;for(b|=4,P=e.current;P!==null;){var s=P,o=s.child;if(P.flags&16){var l=s.deletions;if(l!==null){for(var a=0;a<l.length;a++){var u=l[a];for(P=u;P!==null;){var c=P;switch(c.tag){case 0:case 11:case 15:vr(8,c,s)}var d=c.child;if(d!==null)d.return=c,P=d;else for(;P!==null;){c=P;var f=c.sibling,g=c.return;if(of(c),c===u){P=null;break}if(f!==null){f.return=g,P=f;break}P=g}}}var m=s.alternate;if(m!==null){var w=m.child;if(w!==null){m.child=null;do{var _=w.sibling;w.sibling=null,w=_}while(w!==null)}}P=s}}if(s.subtreeFlags&2064&&o!==null)o.return=s,P=o;else e:for(;P!==null;){if(s=P,s.flags&2048)switch(s.tag){case 0:case 11:case 15:vr(9,s,s.return)}var v=s.sibling;if(v!==null){v.return=s.return,P=v;break e}P=s.return}}var h=e.current;for(P=h;P!==null;){o=P;var p=o.child;if(o.subtreeFlags&2064&&p!==null)p.return=o,P=p;else e:for(o=h;P!==null;){if(l=P,l.flags&2048)try{switch(l.tag){case 0:case 11:case 15:gs(9,l)}}catch(k){q(l,l.return,k)}if(l===o){P=null;break e}var y=l.sibling;if(y!==null){y.return=l.return,P=y;break e}P=l.return}}if(b=i,Lt(),Qe&&typeof Qe.onPostCommitFiberRoot=="function")try{Qe.onPostCommitFiberRoot(as,e)}catch{}r=!0}return r}finally{U=n,$e.transition=t}}return!1}function $u(e,t,n){t=An(n,t),t=Jd(e,t,1),e=xt(e,t,1),t=he(),e!==null&&(Wr(e,1,t),ke(e,t))}function q(e,t,n){if(e.tag===3)$u(e,e,n);else for(;t!==null;){if(t.tag===3){$u(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Ct===null||!Ct.has(r))){e=An(n,e),e=Qd(t,e,1),t=xt(t,e,1),e=he(),t!==null&&(Wr(t,1,e),ke(t,e));break}}t=t.return}}function Tv(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=he(),e.pingedLanes|=e.suspendedLanes&n,re===e&&(se&n)===n&&(Z===4||Z===3&&(se&130023424)===se&&500>Q()-la?Kt(e,0):oa|=n),ke(e,t)}function gf(e,t){t===0&&(e.mode&1?(t=ri,ri<<=1,!(ri&130023424)&&(ri=4194304)):t=1);var n=he();e=ot(e,t),e!==null&&(Wr(e,t,n),ke(e,n))}function jv(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),gf(e,n)}function Ov(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,i=e.memoizedState;i!==null&&(n=i.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(S(314))}r!==null&&r.delete(t),gf(e,n)}var mf;mf=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||_e.current)we=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return we=!1,gv(e,t,n);we=!!(e.flags&131072)}else we=!1,B&&t.flags&1048576&&_d(t,Hi,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Oi(e,t),e=t.pendingProps;var i=Rn(t,de.current);Tn(t,n),i=ea(null,t,r,e,i,n);var s=ta();return t.flags|=1,typeof i=="object"&&i!==null&&typeof i.render=="function"&&i.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Se(r)?(s=!0,Wi(t)):s=!1,t.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,Ql(t),i.updater=vs,t.stateNode=i,i._reactInternals=t,Qo(t,r,e,n),t=Xo(null,t,r,!0,s,n)):(t.tag=0,B&&s&&Bl(t),fe(null,t,i,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Oi(e,t),e=t.pendingProps,i=r._init,r=i(r._payload),t.type=r,i=t.tag=Iv(r),e=ze(r,e),i){case 0:t=Yo(null,t,r,e,n);break e;case 1:t=ku(null,t,r,e,n);break e;case 11:t=_u(null,t,r,e,n);break e;case 14:t=Su(null,t,r,ze(r.type,e),n);break e}throw Error(S(306,r,""))}return t;case 0:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:ze(r,i),Yo(e,t,r,i,n);case 1:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:ze(r,i),ku(e,t,r,i,n);case 3:e:{if(Zd(t),e===null)throw Error(S(387));r=t.pendingProps,s=t.memoizedState,i=s.element,Pd(e,t),Ji(t,r,null,n);var o=t.memoizedState;if(r=o.element,s.isDehydrated)if(s={element:r,isDehydrated:!1,cache:o.cache,pendingSuspenseBoundaries:o.pendingSuspenseBoundaries,transitions:o.transitions},t.updateQueue.baseState=s,t.memoizedState=s,t.flags&256){i=An(Error(S(423)),t),t=Eu(e,t,r,n,i);break e}else if(r!==i){i=An(Error(S(424)),t),t=Eu(e,t,r,n,i);break e}else for(xe=Et(t.stateNode.containerInfo.firstChild),Ce=t,B=!0,Be=null,n=xd(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(In(),r===i){t=lt(e,t,n);break e}fe(e,t,r,n)}t=t.child}return t;case 5:return Td(t),e===null&&qo(t),r=t.type,i=t.pendingProps,s=e!==null?e.memoizedProps:null,o=i.children,Fo(r,i)?o=null:s!==null&&Fo(r,s)&&(t.flags|=32),Xd(e,t),fe(e,t,o,n),t.child;case 6:return e===null&&qo(t),null;case 13:return ef(e,t,n);case 4:return Gl(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=$n(t,null,r,n):fe(e,t,r,n),t.child;case 11:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:ze(r,i),_u(e,t,r,i,n);case 7:return fe(e,t,t.pendingProps,n),t.child;case 8:return fe(e,t,t.pendingProps.children,n),t.child;case 12:return fe(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,i=t.pendingProps,s=t.memoizedProps,o=i.value,z(qi,r._currentValue),r._currentValue=o,s!==null)if(He(s.value,o)){if(s.children===i.children&&!_e.current){t=lt(e,t,n);break e}}else for(s=t.child,s!==null&&(s.return=t);s!==null;){var l=s.dependencies;if(l!==null){o=s.child;for(var a=l.firstContext;a!==null;){if(a.context===r){if(s.tag===1){a=rt(-1,n&-n),a.tag=2;var u=s.updateQueue;if(u!==null){u=u.shared;var c=u.pending;c===null?a.next=a:(a.next=c.next,c.next=a),u.pending=a}}s.lanes|=n,a=s.alternate,a!==null&&(a.lanes|=n),Ko(s.return,n,t),l.lanes|=n;break}a=a.next}}else if(s.tag===10)o=s.type===t.type?null:s.child;else if(s.tag===18){if(o=s.return,o===null)throw Error(S(341));o.lanes|=n,l=o.alternate,l!==null&&(l.lanes|=n),Ko(o,n,t),o=s.sibling}else o=s.child;if(o!==null)o.return=s;else for(o=s;o!==null;){if(o===t){o=null;break}if(s=o.sibling,s!==null){s.return=o.return,o=s;break}o=o.return}s=o}fe(e,t,i.children,n),t=t.child}return t;case 9:return i=t.type,r=t.pendingProps.children,Tn(t,n),i=Ae(i),r=r(i),t.flags|=1,fe(e,t,r,n),t.child;case 14:return r=t.type,i=ze(r,t.pendingProps),i=ze(r.type,i),Su(e,t,r,i,n);case 15:return Gd(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:ze(r,i),Oi(e,t),t.tag=1,Se(r)?(e=!0,Wi(t)):e=!1,Tn(t,n),Kd(t,r,i),Qo(t,r,i,n),Xo(null,t,r,!0,e,n);case 19:return tf(e,t,n);case 22:return Yd(e,t,n)}throw Error(S(156,t.tag))};function yf(e,t){return Vc(e,t)}function Rv(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ie(e,t,n,r){return new Rv(e,t,n,r)}function da(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Iv(e){if(typeof e=="function")return da(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Rl)return 11;if(e===Il)return 14}return 2}function Tt(e,t){var n=e.alternate;return n===null?(n=Ie(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function $i(e,t,n,r,i,s){var o=2;if(r=e,typeof e=="function")da(e)&&(o=1);else if(typeof e=="string")o=5;else e:switch(e){case dn:return Jt(n.children,i,s,t);case Ol:o=8,i|=8;break;case wo:return e=Ie(12,n,t,i|2),e.elementType=wo,e.lanes=s,e;case _o:return e=Ie(13,n,t,i),e.elementType=_o,e.lanes=s,e;case So:return e=Ie(19,n,t,i),e.elementType=So,e.lanes=s,e;case Tc:return ys(n,i,s,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Cc:o=10;break e;case Pc:o=9;break e;case Rl:o=11;break e;case Il:o=14;break e;case dt:o=16,r=null;break e}throw Error(S(130,e==null?e:typeof e,""))}return t=Ie(o,n,t,i),t.elementType=e,t.type=r,t.lanes=s,t}function Jt(e,t,n,r){return e=Ie(7,e,r,t),e.lanes=n,e}function ys(e,t,n,r){return e=Ie(22,e,r,t),e.elementType=Tc,e.lanes=n,e.stateNode={isHidden:!1},e}function oo(e,t,n){return e=Ie(6,e,null,t),e.lanes=n,e}function lo(e,t,n){return t=Ie(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function $v(e,t,n,r,i){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Fs(0),this.expirationTimes=Fs(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Fs(0),this.identifierPrefix=r,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function fa(e,t,n,r,i,s,o,l,a){return e=new $v(e,t,n,l,a),t===1?(t=1,s===!0&&(t|=8)):t=0,s=Ie(3,null,null,t),e.current=s,s.stateNode=e,s.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Ql(s),e}function Lv(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:cn,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function wf(e){if(!e)return Rt;e=e._reactInternals;e:{if(tn(e)!==e||e.tag!==1)throw Error(S(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Se(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(S(171))}if(e.tag===1){var n=e.type;if(Se(n))return yd(e,n,t)}return t}function _f(e,t,n,r,i,s,o,l,a){return e=fa(n,r,!0,e,i,s,o,l,a),e.context=wf(null),n=e.current,r=he(),i=Pt(n),s=rt(r,i),s.callback=t??null,xt(n,s,i),e.current.lanes=i,Wr(e,i,r),ke(e,r),e}function ws(e,t,n,r){var i=t.current,s=he(),o=Pt(i);return n=wf(n),t.context===null?t.context=n:t.pendingContext=n,t=rt(s,o),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=xt(i,t,o),e!==null&&(Ve(e,i,o,s),Pi(e,i,o)),o}function ns(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Lu(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function ha(e,t){Lu(e,t),(e=e.alternate)&&Lu(e,t)}function Av(){return null}var Sf=typeof reportError=="function"?reportError:function(e){console.error(e)};function pa(e){this._internalRoot=e}_s.prototype.render=pa.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(S(409));ws(e,t,null,null)};_s.prototype.unmount=pa.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Zt(function(){ws(null,e,null,null)}),t[st]=null}};function _s(e){this._internalRoot=e}_s.prototype.unstable_scheduleHydration=function(e){if(e){var t=Yc();e={blockedOn:null,target:e,priority:t};for(var n=0;n<pt.length&&t!==0&&t<pt[n].priority;n++);pt.splice(n,0,e),n===0&&Zc(e)}};function va(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Ss(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Au(){}function Nv(e,t,n,r,i){if(i){if(typeof r=="function"){var s=r;r=function(){var u=ns(o);s.call(u)}}var o=_f(t,r,e,0,null,!1,!1,"",Au);return e._reactRootContainer=o,e[st]=o.current,Rr(e.nodeType===8?e.parentNode:e),Zt(),o}for(;i=e.lastChild;)e.removeChild(i);if(typeof r=="function"){var l=r;r=function(){var u=ns(a);l.call(u)}}var a=fa(e,0,!1,null,null,!1,!1,"",Au);return e._reactRootContainer=a,e[st]=a.current,Rr(e.nodeType===8?e.parentNode:e),Zt(function(){ws(t,a,n,r)}),a}function ks(e,t,n,r,i){var s=n._reactRootContainer;if(s){var o=s;if(typeof i=="function"){var l=i;i=function(){var a=ns(o);l.call(a)}}ws(t,o,e,i)}else o=Nv(n,t,e,i,r);return ns(o)}Qc=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=sr(t.pendingLanes);n!==0&&(Al(t,n|1),ke(t,Q()),!(b&6)&&(Nn=Q()+500,Lt()))}break;case 13:Zt(function(){var r=ot(e,1);if(r!==null){var i=he();Ve(r,e,1,i)}}),ha(e,1)}};Nl=function(e){if(e.tag===13){var t=ot(e,134217728);if(t!==null){var n=he();Ve(t,e,134217728,n)}ha(e,134217728)}};Gc=function(e){if(e.tag===13){var t=Pt(e),n=ot(e,t);if(n!==null){var r=he();Ve(n,e,t,r)}ha(e,t)}};Yc=function(){return U};Xc=function(e,t){var n=U;try{return U=e,t()}finally{U=n}};Io=function(e,t,n){switch(t){case"input":if(xo(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var i=fs(r);if(!i)throw Error(S(90));Oc(r),xo(r,i)}}}break;case"textarea":Ic(e,n);break;case"select":t=n.value,t!=null&&En(e,!!n.multiple,t,!1)}};Dc=aa;zc=Zt;var bv={usingClientEntryPoint:!1,Events:[Hr,vn,fs,bc,Uc,aa]},tr={findFiberByHostInstance:Vt,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Uv={bundleType:tr.bundleType,version:tr.version,rendererPackageName:tr.rendererPackageName,rendererConfig:tr.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:at.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Bc(e),e===null?null:e.stateNode},findFiberByHostInstance:tr.findFiberByHostInstance||Av,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var pi=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!pi.isDisabled&&pi.supportsFiber)try{as=pi.inject(Uv),Qe=pi}catch{}}Te.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=bv;Te.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!va(t))throw Error(S(200));return Lv(e,t,null,n)};Te.createRoot=function(e,t){if(!va(e))throw Error(S(299));var n=!1,r="",i=Sf;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(i=t.onRecoverableError)),t=fa(e,1,!1,null,null,n,!1,r,i),e[st]=t.current,Rr(e.nodeType===8?e.parentNode:e),new pa(t)};Te.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(S(188)):(e=Object.keys(e).join(","),Error(S(268,e)));return e=Bc(t),e=e===null?null:e.stateNode,e};Te.flushSync=function(e){return Zt(e)};Te.hydrate=function(e,t,n){if(!Ss(t))throw Error(S(200));return ks(null,e,t,!0,n)};Te.hydrateRoot=function(e,t,n){if(!va(e))throw Error(S(405));var r=n!=null&&n.hydratedSources||null,i=!1,s="",o=Sf;if(n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(s=n.identifierPrefix),n.onRecoverableError!==void 0&&(o=n.onRecoverableError)),t=_f(t,null,e,1,n??null,i,!1,s,o),e[st]=t.current,Rr(e),r)for(e=0;e<r.length;e++)n=r[e],i=n._getVersion,i=i(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,i]:t.mutableSourceEagerHydrationData.push(n,i);return new _s(t)};Te.render=function(e,t,n){if(!Ss(t))throw Error(S(200));return ks(null,e,t,!1,n)};Te.unmountComponentAtNode=function(e){if(!Ss(e))throw Error(S(40));return e._reactRootContainer?(Zt(function(){ks(null,null,e,!1,function(){e._reactRootContainer=null,e[st]=null})}),!0):!1};Te.unstable_batchedUpdates=aa;Te.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Ss(n))throw Error(S(200));if(e==null||e._reactInternals===void 0)throw Error(S(38));return ks(e,t,n,!1,r)};Te.version="18.3.1-next-f1338f8080-20240426";function kf(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(kf)}catch(e){console.error(e)}}kf(),Sc.exports=Te;var Dv=Sc.exports,Nu=Dv;mo.createRoot=Nu.createRoot,mo.hydrateRoot=Nu.hydrateRoot;/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function zr(){return zr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},zr.apply(this,arguments)}var wt;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(wt||(wt={}));const bu="popstate";function zv(e){e===void 0&&(e={});function t(r,i){let{pathname:s,search:o,hash:l}=r.location;return cl("",{pathname:s,search:o,hash:l},i.state&&i.state.usr||null,i.state&&i.state.key||"default")}function n(r,i){return typeof i=="string"?i:rs(i)}return Fv(t,n,null,e)}function G(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function Ef(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function Mv(){return Math.random().toString(36).substr(2,8)}function Uu(e,t){return{usr:e.state,key:e.key,idx:t}}function cl(e,t,n,r){return n===void 0&&(n=null),zr({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?Fn(t):t,{state:n,key:t&&t.key||r||Mv()})}function rs(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function Fn(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function Fv(e,t,n,r){r===void 0&&(r={});let{window:i=document.defaultView,v5Compat:s=!1}=r,o=i.history,l=wt.Pop,a=null,u=c();u==null&&(u=0,o.replaceState(zr({},o.state,{idx:u}),""));function c(){return(o.state||{idx:null}).idx}function d(){l=wt.Pop;let _=c(),v=_==null?null:_-u;u=_,a&&a({action:l,location:w.location,delta:v})}function f(_,v){l=wt.Push;let h=cl(w.location,_,v);n&&n(h,_),u=c()+1;let p=Uu(h,u),y=w.createHref(h);try{o.pushState(p,"",y)}catch(k){if(k instanceof DOMException&&k.name==="DataCloneError")throw k;i.location.assign(y)}s&&a&&a({action:l,location:w.location,delta:1})}function g(_,v){l=wt.Replace;let h=cl(w.location,_,v);n&&n(h,_),u=c();let p=Uu(h,u),y=w.createHref(h);o.replaceState(p,"",y),s&&a&&a({action:l,location:w.location,delta:0})}function m(_){let v=i.location.origin!=="null"?i.location.origin:i.location.href,h=typeof _=="string"?_:rs(_);return h=h.replace(/ $/,"%20"),G(v,"No window.location.(origin|href) available to create URL for href: "+h),new URL(h,v)}let w={get action(){return l},get location(){return e(i,o)},listen(_){if(a)throw new Error("A history only accepts one active listener");return i.addEventListener(bu,d),a=_,()=>{i.removeEventListener(bu,d),a=null}},createHref(_){return t(i,_)},createURL:m,encodeLocation(_){let v=m(_);return{pathname:v.pathname,search:v.search,hash:v.hash}},push:f,replace:g,go(_){return o.go(_)}};return w}var Du;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(Du||(Du={}));function Bv(e,t,n){return n===void 0&&(n="/"),Wv(e,t,n,!1)}function Wv(e,t,n,r){let i=typeof t=="string"?Fn(t):t,s=ga(i.pathname||"/",n);if(s==null)return null;let o=xf(e);Vv(o);let l=null;for(let a=0;l==null&&a<o.length;++a){let u=tg(s);l=Zv(o[a],u,r)}return l}function xf(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let i=(s,o,l)=>{let a={relativePath:l===void 0?s.path||"":l,caseSensitive:s.caseSensitive===!0,childrenIndex:o,route:s};a.relativePath.startsWith("/")&&(G(a.relativePath.startsWith(r),'Absolute route path "'+a.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),a.relativePath=a.relativePath.slice(r.length));let u=jt([r,a.relativePath]),c=n.concat(a);s.children&&s.children.length>0&&(G(s.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+u+'".')),xf(s.children,t,c,u)),!(s.path==null&&!s.index)&&t.push({path:u,score:Yv(u,s.index),routesMeta:c})};return e.forEach((s,o)=>{var l;if(s.path===""||!((l=s.path)!=null&&l.includes("?")))i(s,o);else for(let a of Cf(s.path))i(s,o,a)}),t}function Cf(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,i=n.endsWith("?"),s=n.replace(/\?$/,"");if(r.length===0)return i?[s,""]:[s];let o=Cf(r.join("/")),l=[];return l.push(...o.map(a=>a===""?s:[s,a].join("/"))),i&&l.push(...o),l.map(a=>e.startsWith("/")&&a===""?"/":a)}function Vv(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:Xv(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const Hv=/^:[\w-]+$/,qv=3,Kv=2,Jv=1,Qv=10,Gv=-2,zu=e=>e==="*";function Yv(e,t){let n=e.split("/"),r=n.length;return n.some(zu)&&(r+=Gv),t&&(r+=Kv),n.filter(i=>!zu(i)).reduce((i,s)=>i+(Hv.test(s)?qv:s===""?Jv:Qv),r)}function Xv(e,t){return e.length===t.length&&e.slice(0,-1).every((r,i)=>r===t[i])?e[e.length-1]-t[t.length-1]:0}function Zv(e,t,n){n===void 0&&(n=!1);let{routesMeta:r}=e,i={},s="/",o=[];for(let l=0;l<r.length;++l){let a=r[l],u=l===r.length-1,c=s==="/"?t:t.slice(s.length)||"/",d=Mu({path:a.relativePath,caseSensitive:a.caseSensitive,end:u},c),f=a.route;if(!d&&u&&n&&!r[r.length-1].route.index&&(d=Mu({path:a.relativePath,caseSensitive:a.caseSensitive,end:!1},c)),!d)return null;Object.assign(i,d.params),o.push({params:i,pathname:jt([s,d.pathname]),pathnameBase:sg(jt([s,d.pathnameBase])),route:f}),d.pathnameBase!=="/"&&(s=jt([s,d.pathnameBase]))}return o}function Mu(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=eg(e.path,e.caseSensitive,e.end),i=t.match(n);if(!i)return null;let s=i[0],o=s.replace(/(.)\/+$/,"$1"),l=i.slice(1);return{params:r.reduce((u,c,d)=>{let{paramName:f,isOptional:g}=c;if(f==="*"){let w=l[d]||"";o=s.slice(0,s.length-w.length).replace(/(.)\/+$/,"$1")}const m=l[d];return g&&!m?u[f]=void 0:u[f]=(m||"").replace(/%2F/g,"/"),u},{}),pathname:s,pathnameBase:o,pattern:e}}function eg(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),Ef(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],i="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(o,l,a)=>(r.push({paramName:l,isOptional:a!=null}),a?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),i+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?i+="\\/*$":e!==""&&e!=="/"&&(i+="(?:(?=\\/|$))"),[new RegExp(i,t?void 0:"i"),r]}function tg(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return Ef(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function ga(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function ng(e,t){t===void 0&&(t="/");let{pathname:n,search:r="",hash:i=""}=typeof e=="string"?Fn(e):e;return{pathname:n?n.startsWith("/")?n:rg(n,t):t,search:og(r),hash:lg(i)}}function rg(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(i=>{i===".."?n.length>1&&n.pop():i!=="."&&n.push(i)}),n.length>1?n.join("/"):"/"}function ao(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function ig(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function ma(e,t){let n=ig(e);return t?n.map((r,i)=>i===n.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function ya(e,t,n,r){r===void 0&&(r=!1);let i;typeof e=="string"?i=Fn(e):(i=zr({},e),G(!i.pathname||!i.pathname.includes("?"),ao("?","pathname","search",i)),G(!i.pathname||!i.pathname.includes("#"),ao("#","pathname","hash",i)),G(!i.search||!i.search.includes("#"),ao("#","search","hash",i)));let s=e===""||i.pathname==="",o=s?"/":i.pathname,l;if(o==null)l=n;else{let d=t.length-1;if(!r&&o.startsWith("..")){let f=o.split("/");for(;f[0]==="..";)f.shift(),d-=1;i.pathname=f.join("/")}l=d>=0?t[d]:"/"}let a=ng(i,l),u=o&&o!=="/"&&o.endsWith("/"),c=(s||o===".")&&n.endsWith("/");return!a.pathname.endsWith("/")&&(u||c)&&(a.pathname+="/"),a}const jt=e=>e.join("/").replace(/\/\/+/g,"/"),sg=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),og=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,lg=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function ag(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const Pf=["post","put","patch","delete"];new Set(Pf);const ug=["get",...Pf];new Set(ug);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Mr(){return Mr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Mr.apply(this,arguments)}const wa=x.createContext(null),cg=x.createContext(null),At=x.createContext(null),Es=x.createContext(null),Nt=x.createContext({outlet:null,matches:[],isDataRoute:!1}),Tf=x.createContext(null);function dg(e,t){let{relative:n}=t===void 0?{}:t;Bn()||G(!1);let{basename:r,navigator:i}=x.useContext(At),{hash:s,pathname:o,search:l}=Of(e,{relative:n}),a=o;return r!=="/"&&(a=o==="/"?r:jt([r,o])),i.createHref({pathname:a,search:l,hash:s})}function Bn(){return x.useContext(Es)!=null}function Wn(){return Bn()||G(!1),x.useContext(Es).location}function jf(e){x.useContext(At).static||x.useLayoutEffect(e)}function xs(){let{isDataRoute:e}=x.useContext(Nt);return e?xg():fg()}function fg(){Bn()||G(!1);let e=x.useContext(wa),{basename:t,future:n,navigator:r}=x.useContext(At),{matches:i}=x.useContext(Nt),{pathname:s}=Wn(),o=JSON.stringify(ma(i,n.v7_relativeSplatPath)),l=x.useRef(!1);return jf(()=>{l.current=!0}),x.useCallback(function(u,c){if(c===void 0&&(c={}),!l.current)return;if(typeof u=="number"){r.go(u);return}let d=ya(u,JSON.parse(o),s,c.relative==="path");e==null&&t!=="/"&&(d.pathname=d.pathname==="/"?t:jt([t,d.pathname])),(c.replace?r.replace:r.push)(d,c.state,c)},[t,r,o,s,e])}function Of(e,t){let{relative:n}=t===void 0?{}:t,{future:r}=x.useContext(At),{matches:i}=x.useContext(Nt),{pathname:s}=Wn(),o=JSON.stringify(ma(i,r.v7_relativeSplatPath));return x.useMemo(()=>ya(e,JSON.parse(o),s,n==="path"),[e,o,s,n])}function hg(e,t){return pg(e,t)}function pg(e,t,n,r){Bn()||G(!1);let{navigator:i}=x.useContext(At),{matches:s}=x.useContext(Nt),o=s[s.length-1],l=o?o.params:{};o&&o.pathname;let a=o?o.pathnameBase:"/";o&&o.route;let u=Wn(),c;if(t){var d;let _=typeof t=="string"?Fn(t):t;a==="/"||(d=_.pathname)!=null&&d.startsWith(a)||G(!1),c=_}else c=u;let f=c.pathname||"/",g=f;if(a!=="/"){let _=a.replace(/^\//,"").split("/");g="/"+f.replace(/^\//,"").split("/").slice(_.length).join("/")}let m=Bv(e,{pathname:g}),w=wg(m&&m.map(_=>Object.assign({},_,{params:Object.assign({},l,_.params),pathname:jt([a,i.encodeLocation?i.encodeLocation(_.pathname).pathname:_.pathname]),pathnameBase:_.pathnameBase==="/"?a:jt([a,i.encodeLocation?i.encodeLocation(_.pathnameBase).pathname:_.pathnameBase])})),s,n,r);return t&&w?x.createElement(Es.Provider,{value:{location:Mr({pathname:"/",search:"",hash:"",state:null,key:"default"},c),navigationType:wt.Pop}},w):w}function vg(){let e=Eg(),t=ag(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,i={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"},s=null;return x.createElement(x.Fragment,null,x.createElement("h2",null,"Unexpected Application Error!"),x.createElement("h3",{style:{fontStyle:"italic"}},t),n?x.createElement("pre",{style:i},n):null,s)}const gg=x.createElement(vg,null);class mg extends x.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?x.createElement(Nt.Provider,{value:this.props.routeContext},x.createElement(Tf.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function yg(e){let{routeContext:t,match:n,children:r}=e,i=x.useContext(wa);return i&&i.static&&i.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(i.staticContext._deepestRenderedBoundaryId=n.route.id),x.createElement(Nt.Provider,{value:t},r)}function wg(e,t,n,r){var i;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var s;if(!n)return null;if(n.errors)e=n.matches;else if((s=r)!=null&&s.v7_partialHydration&&t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let o=e,l=(i=n)==null?void 0:i.errors;if(l!=null){let c=o.findIndex(d=>d.route.id&&(l==null?void 0:l[d.route.id])!==void 0);c>=0||G(!1),o=o.slice(0,Math.min(o.length,c+1))}let a=!1,u=-1;if(n&&r&&r.v7_partialHydration)for(let c=0;c<o.length;c++){let d=o[c];if((d.route.HydrateFallback||d.route.hydrateFallbackElement)&&(u=c),d.route.id){let{loaderData:f,errors:g}=n,m=d.route.loader&&f[d.route.id]===void 0&&(!g||g[d.route.id]===void 0);if(d.route.lazy||m){a=!0,u>=0?o=o.slice(0,u+1):o=[o[0]];break}}}return o.reduceRight((c,d,f)=>{let g,m=!1,w=null,_=null;n&&(g=l&&d.route.id?l[d.route.id]:void 0,w=d.route.errorElement||gg,a&&(u<0&&f===0?(Cg("route-fallback",!1),m=!0,_=null):u===f&&(m=!0,_=d.route.hydrateFallbackElement||null)));let v=t.concat(o.slice(0,f+1)),h=()=>{let p;return g?p=w:m?p=_:d.route.Component?p=x.createElement(d.route.Component,null):d.route.element?p=d.route.element:p=c,x.createElement(yg,{match:d,routeContext:{outlet:c,matches:v,isDataRoute:n!=null},children:p})};return n&&(d.route.ErrorBoundary||d.route.errorElement||f===0)?x.createElement(mg,{location:n.location,revalidation:n.revalidation,component:w,error:g,children:h(),routeContext:{outlet:null,matches:v,isDataRoute:!0}}):h()},null)}var Rf=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(Rf||{}),is=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(is||{});function _g(e){let t=x.useContext(wa);return t||G(!1),t}function Sg(e){let t=x.useContext(cg);return t||G(!1),t}function kg(e){let t=x.useContext(Nt);return t||G(!1),t}function If(e){let t=kg(),n=t.matches[t.matches.length-1];return n.route.id||G(!1),n.route.id}function Eg(){var e;let t=x.useContext(Tf),n=Sg(is.UseRouteError),r=If(is.UseRouteError);return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}function xg(){let{router:e}=_g(Rf.UseNavigateStable),t=If(is.UseNavigateStable),n=x.useRef(!1);return jf(()=>{n.current=!0}),x.useCallback(function(i,s){s===void 0&&(s={}),n.current&&(typeof i=="number"?e.navigate(i):e.navigate(i,Mr({fromRouteId:t},s)))},[e,t])}const Fu={};function Cg(e,t,n){!t&&!Fu[e]&&(Fu[e]=!0)}function Pg(e,t){e==null||e.v7_startTransition,(e==null?void 0:e.v7_relativeSplatPath)===void 0&&(!t||t.v7_relativeSplatPath),t&&(t.v7_fetcherPersist,t.v7_normalizeFormMethod,t.v7_partialHydration,t.v7_skipActionErrorRevalidation)}function $f(e){let{to:t,replace:n,state:r,relative:i}=e;Bn()||G(!1);let{future:s,static:o}=x.useContext(At),{matches:l}=x.useContext(Nt),{pathname:a}=Wn(),u=xs(),c=ya(t,ma(l,s.v7_relativeSplatPath),a,i==="path"),d=JSON.stringify(c);return x.useEffect(()=>u(JSON.parse(d),{replace:n,state:r,relative:i}),[u,d,i,n,r]),null}function lr(e){G(!1)}function Tg(e){let{basename:t="/",children:n=null,location:r,navigationType:i=wt.Pop,navigator:s,static:o=!1,future:l}=e;Bn()&&G(!1);let a=t.replace(/^\/*/,"/"),u=x.useMemo(()=>({basename:a,navigator:s,static:o,future:Mr({v7_relativeSplatPath:!1},l)}),[a,l,s,o]);typeof r=="string"&&(r=Fn(r));let{pathname:c="/",search:d="",hash:f="",state:g=null,key:m="default"}=r,w=x.useMemo(()=>{let _=ga(c,a);return _==null?null:{location:{pathname:_,search:d,hash:f,state:g,key:m},navigationType:i}},[a,c,d,f,g,m,i]);return w==null?null:x.createElement(At.Provider,{value:u},x.createElement(Es.Provider,{children:n,value:w}))}function jg(e){let{children:t,location:n}=e;return hg(dl(t),n)}new Promise(()=>{});function dl(e,t){t===void 0&&(t=[]);let n=[];return x.Children.forEach(e,(r,i)=>{if(!x.isValidElement(r))return;let s=[...t,i];if(r.type===x.Fragment){n.push.apply(n,dl(r.props.children,s));return}r.type!==lr&&G(!1),!r.props.index||!r.props.children||G(!1);let o={id:r.props.id||s.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(o.children=dl(r.props.children,s)),n.push(o)}),n}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function fl(){return fl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},fl.apply(this,arguments)}function Og(e,t){if(e==null)return{};var n={},r=Object.keys(e),i,s;for(s=0;s<r.length;s++)i=r[s],!(t.indexOf(i)>=0)&&(n[i]=e[i]);return n}function Rg(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function Ig(e,t){return e.button===0&&(!t||t==="_self")&&!Rg(e)}const $g=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],Lg="6";try{window.__reactRouterVersion=Lg}catch{}const Ag="startTransition",Bu=Th[Ag];function Ng(e){let{basename:t,children:n,future:r,window:i}=e,s=x.useRef();s.current==null&&(s.current=zv({window:i,v5Compat:!0}));let o=s.current,[l,a]=x.useState({action:o.action,location:o.location}),{v7_startTransition:u}=r||{},c=x.useCallback(d=>{u&&Bu?Bu(()=>a(d)):a(d)},[a,u]);return x.useLayoutEffect(()=>o.listen(c),[o,c]),x.useEffect(()=>Pg(r),[r]),x.createElement(Tg,{basename:t,children:n,location:l.location,navigationType:l.action,navigator:o,future:r})}const bg=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",Ug=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Lf=x.forwardRef(function(t,n){let{onClick:r,relative:i,reloadDocument:s,replace:o,state:l,target:a,to:u,preventScrollReset:c,viewTransition:d}=t,f=Og(t,$g),{basename:g}=x.useContext(At),m,w=!1;if(typeof u=="string"&&Ug.test(u)&&(m=u,bg))try{let p=new URL(window.location.href),y=u.startsWith("//")?new URL(p.protocol+u):new URL(u),k=ga(y.pathname,g);y.origin===p.origin&&k!=null?u=k+y.search+y.hash:w=!0}catch{}let _=dg(u,{relative:i}),v=Dg(u,{replace:o,state:l,target:a,preventScrollReset:c,relative:i,viewTransition:d});function h(p){r&&r(p),p.defaultPrevented||v(p)}return x.createElement("a",fl({},f,{href:m||_,onClick:w||s?r:h,ref:n,target:a}))});var Wu;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(Wu||(Wu={}));var Vu;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(Vu||(Vu={}));function Dg(e,t){let{target:n,replace:r,state:i,preventScrollReset:s,relative:o,viewTransition:l}=t===void 0?{}:t,a=xs(),u=Wn(),c=Of(e,{relative:o});return x.useCallback(d=>{if(Ig(d,n)){d.preventDefault();let f=r!==void 0?r:rs(u)===rs(c);a(e,{replace:f,state:i,preventScrollReset:s,relative:o,viewTransition:l})}},[u,a,c,r,i,n,e,s,o,l])}const Hu=e=>{let t;const n=new Set,r=(c,d)=>{const f=typeof c=="function"?c(t):c;if(!Object.is(f,t)){const g=t;t=d??(typeof f!="object"||f===null)?f:Object.assign({},t,f),n.forEach(m=>m(t,g))}},i=()=>t,a={setState:r,getState:i,getInitialState:()=>u,subscribe:c=>(n.add(c),()=>n.delete(c)),destroy:()=>{n.clear()}},u=t=e(r,i,a);return a},zg=e=>e?Hu(e):Hu;var Af={exports:{}},Nf={},bf={exports:{}},Uf={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var bn=x;function Mg(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Fg=typeof Object.is=="function"?Object.is:Mg,Bg=bn.useState,Wg=bn.useEffect,Vg=bn.useLayoutEffect,Hg=bn.useDebugValue;function qg(e,t){var n=t(),r=Bg({inst:{value:n,getSnapshot:t}}),i=r[0].inst,s=r[1];return Vg(function(){i.value=n,i.getSnapshot=t,uo(i)&&s({inst:i})},[e,n,t]),Wg(function(){return uo(i)&&s({inst:i}),e(function(){uo(i)&&s({inst:i})})},[e]),Hg(n),n}function uo(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Fg(e,n)}catch{return!0}}function Kg(e,t){return t()}var Jg=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?Kg:qg;Uf.useSyncExternalStore=bn.useSyncExternalStore!==void 0?bn.useSyncExternalStore:Jg;bf.exports=Uf;var Qg=bf.exports;/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Cs=x,Gg=Qg;function Yg(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Xg=typeof Object.is=="function"?Object.is:Yg,Zg=Gg.useSyncExternalStore,em=Cs.useRef,tm=Cs.useEffect,nm=Cs.useMemo,rm=Cs.useDebugValue;Nf.useSyncExternalStoreWithSelector=function(e,t,n,r,i){var s=em(null);if(s.current===null){var o={hasValue:!1,value:null};s.current=o}else o=s.current;s=nm(function(){function a(g){if(!u){if(u=!0,c=g,g=r(g),i!==void 0&&o.hasValue){var m=o.value;if(i(m,g))return d=m}return d=g}if(m=d,Xg(c,g))return m;var w=r(g);return i!==void 0&&i(m,w)?(c=g,m):(c=g,d=w)}var u=!1,c,d,f=n===void 0?null:n;return[function(){return a(t())},f===null?void 0:function(){return a(f())}]},[t,n,r,i]);var l=Zg(e,s[0],s[1]);return tm(function(){o.hasValue=!0,o.value=l},[l]),rm(l),l};Af.exports=Nf;var im=Af.exports;const sm=uc(im),{useDebugValue:om}=Cl,{useSyncExternalStoreWithSelector:lm}=sm;const am=e=>e;function um(e,t=am,n){const r=lm(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,n);return om(r),r}const qu=e=>{const t=typeof e=="function"?zg(e):e,n=(r,i)=>um(t,r,i);return Object.assign(n,t),n},cm=e=>e?qu(e):qu,dm="modulepreload",fm=function(e){return"/"+e},Ku={},Kr=function(t,n,r){if(!n||n.length===0)return t();const i=document.getElementsByTagName("link");return Promise.all(n.map(s=>{if(s=fm(s),s in Ku)return;Ku[s]=!0;const o=s.endsWith(".css"),l=o?'[rel="stylesheet"]':"";if(!!r)for(let c=i.length-1;c>=0;c--){const d=i[c];if(d.href===s&&(!o||d.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${s}"]${l}`))return;const u=document.createElement("link");if(u.rel=o?"stylesheet":dm,o||(u.as="script",u.crossOrigin=""),u.href=s,document.head.appendChild(u),o)return new Promise((c,d)=>{u.addEventListener("load",c),u.addEventListener("error",()=>d(new Error(`Unable to preload CSS for ${s}`)))})})).then(()=>t()).catch(s=>{const o=new Event("vite:preloadError",{cancelable:!0});if(o.payload=s,window.dispatchEvent(o),!o.defaultPrevented)throw s})},hm=e=>{let t;return e?t=e:typeof fetch>"u"?t=(...n)=>Kr(()=>Promise.resolve().then(()=>Vn),void 0).then(({default:r})=>r(...n)):t=fetch,(...n)=>t(...n)};class _a extends Error{constructor(t,n="FunctionsError",r){super(t),this.name=n,this.context=r}}class pm extends _a{constructor(t){super("Failed to send a request to the Edge Function","FunctionsFetchError",t)}}class vm extends _a{constructor(t){super("Relay Error invoking the Edge Function","FunctionsRelayError",t)}}class gm extends _a{constructor(t){super("Edge Function returned a non-2xx status code","FunctionsHttpError",t)}}var hl;(function(e){e.Any="any",e.ApNortheast1="ap-northeast-1",e.ApNortheast2="ap-northeast-2",e.ApSouth1="ap-south-1",e.ApSoutheast1="ap-southeast-1",e.ApSoutheast2="ap-southeast-2",e.CaCentral1="ca-central-1",e.EuCentral1="eu-central-1",e.EuWest1="eu-west-1",e.EuWest2="eu-west-2",e.EuWest3="eu-west-3",e.SaEast1="sa-east-1",e.UsEast1="us-east-1",e.UsWest1="us-west-1",e.UsWest2="us-west-2"})(hl||(hl={}));var mm=globalThis&&globalThis.__awaiter||function(e,t,n,r){function i(s){return s instanceof n?s:new n(function(o){o(s)})}return new(n||(n=Promise))(function(s,o){function l(c){try{u(r.next(c))}catch(d){o(d)}}function a(c){try{u(r.throw(c))}catch(d){o(d)}}function u(c){c.done?s(c.value):i(c.value).then(l,a)}u((r=r.apply(e,t||[])).next())})};class ym{constructor(t,{headers:n={},customFetch:r,region:i=hl.Any}={}){this.url=t,this.headers=n,this.region=i,this.fetch=hm(r)}setAuth(t){this.headers.Authorization=`Bearer ${t}`}invoke(t,n={}){var r;return mm(this,void 0,void 0,function*(){try{const{headers:i,method:s,body:o}=n;let l={},{region:a}=n;a||(a=this.region),a&&a!=="any"&&(l["x-region"]=a);let u;o&&(i&&!Object.prototype.hasOwnProperty.call(i,"Content-Type")||!i)&&(typeof Blob<"u"&&o instanceof Blob||o instanceof ArrayBuffer?(l["Content-Type"]="application/octet-stream",u=o):typeof o=="string"?(l["Content-Type"]="text/plain",u=o):typeof FormData<"u"&&o instanceof FormData?u=o:(l["Content-Type"]="application/json",u=JSON.stringify(o)));const c=yield this.fetch(`${this.url}/${t}`,{method:s||"POST",headers:Object.assign(Object.assign(Object.assign({},l),this.headers),i),body:u}).catch(m=>{throw new pm(m)}),d=c.headers.get("x-relay-error");if(d&&d==="true")throw new vm(c);if(!c.ok)throw new gm(c);let f=((r=c.headers.get("Content-Type"))!==null&&r!==void 0?r:"text/plain").split(";")[0].trim(),g;return f==="application/json"?g=yield c.json():f==="application/octet-stream"?g=yield c.blob():f==="text/event-stream"?g=c:f==="multipart/form-data"?g=yield c.formData():g=yield c.text(),{data:g,error:null}}catch(i){return{data:null,error:i}}})}}var ye={},Sa={},Ps={},Jr={},Ts={},js={},wm=function(){if(typeof self<"u")return self;if(typeof window<"u")return window;if(typeof global<"u")return global;throw new Error("unable to locate global object")},Un=wm();const _m=Un.fetch,Df=Un.fetch.bind(Un),zf=Un.Headers,Sm=Un.Request,km=Un.Response,Vn=Object.freeze(Object.defineProperty({__proto__:null,Headers:zf,Request:Sm,Response:km,default:Df,fetch:_m},Symbol.toStringTag,{value:"Module"})),Em=dh(Vn);var Os={};Object.defineProperty(Os,"__esModule",{value:!0});let xm=class extends Error{constructor(t){super(t.message),this.name="PostgrestError",this.details=t.details,this.hint=t.hint,this.code=t.code}};Os.default=xm;var Mf=Le&&Le.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(js,"__esModule",{value:!0});const Cm=Mf(Em),Pm=Mf(Os);let Tm=class{constructor(t){this.shouldThrowOnError=!1,this.method=t.method,this.url=t.url,this.headers=t.headers,this.schema=t.schema,this.body=t.body,this.shouldThrowOnError=t.shouldThrowOnError,this.signal=t.signal,this.isMaybeSingle=t.isMaybeSingle,t.fetch?this.fetch=t.fetch:typeof fetch>"u"?this.fetch=Cm.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(t,n){return this.headers=Object.assign({},this.headers),this.headers[t]=n,this}then(t,n){this.schema===void 0||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),this.method!=="GET"&&this.method!=="HEAD"&&(this.headers["Content-Type"]="application/json");const r=this.fetch;let i=r(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then(async s=>{var o,l,a;let u=null,c=null,d=null,f=s.status,g=s.statusText;if(s.ok){if(this.method!=="HEAD"){const v=await s.text();v===""||(this.headers.Accept==="text/csv"||this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?c=v:c=JSON.parse(v))}const w=(o=this.headers.Prefer)===null||o===void 0?void 0:o.match(/count=(exact|planned|estimated)/),_=(l=s.headers.get("content-range"))===null||l===void 0?void 0:l.split("/");w&&_&&_.length>1&&(d=parseInt(_[1])),this.isMaybeSingle&&this.method==="GET"&&Array.isArray(c)&&(c.length>1?(u={code:"PGRST116",details:`Results contain ${c.length} rows, application/vnd.pgrst.object+json requires 1 row`,hint:null,message:"JSON object requested, multiple (or no) rows returned"},c=null,d=null,f=406,g="Not Acceptable"):c.length===1?c=c[0]:c=null)}else{const w=await s.text();try{u=JSON.parse(w),Array.isArray(u)&&s.status===404&&(c=[],u=null,f=200,g="OK")}catch{s.status===404&&w===""?(f=204,g="No Content"):u={message:w}}if(u&&this.isMaybeSingle&&(!((a=u==null?void 0:u.details)===null||a===void 0)&&a.includes("0 rows"))&&(u=null,f=200,g="OK"),u&&this.shouldThrowOnError)throw new Pm.default(u)}return{error:u,data:c,count:d,status:f,statusText:g}});return this.shouldThrowOnError||(i=i.catch(s=>{var o,l,a;return{error:{message:`${(o=s==null?void 0:s.name)!==null&&o!==void 0?o:"FetchError"}: ${s==null?void 0:s.message}`,details:`${(l=s==null?void 0:s.stack)!==null&&l!==void 0?l:""}`,hint:"",code:`${(a=s==null?void 0:s.code)!==null&&a!==void 0?a:""}`},data:null,count:null,status:0,statusText:""}})),i.then(t,n)}returns(){return this}overrideTypes(){return this}};js.default=Tm;var jm=Le&&Le.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Ts,"__esModule",{value:!0});const Om=jm(js);let Rm=class extends Om.default{select(t){let n=!1;const r=(t??"*").split("").map(i=>/\s/.test(i)&&!n?"":(i==='"'&&(n=!n),i)).join("");return this.url.searchParams.set("select",r),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(t,{ascending:n=!0,nullsFirst:r,foreignTable:i,referencedTable:s=i}={}){const o=s?`${s}.order`:"order",l=this.url.searchParams.get(o);return this.url.searchParams.set(o,`${l?`${l},`:""}${t}.${n?"asc":"desc"}${r===void 0?"":r?".nullsfirst":".nullslast"}`),this}limit(t,{foreignTable:n,referencedTable:r=n}={}){const i=typeof r>"u"?"limit":`${r}.limit`;return this.url.searchParams.set(i,`${t}`),this}range(t,n,{foreignTable:r,referencedTable:i=r}={}){const s=typeof i>"u"?"offset":`${i}.offset`,o=typeof i>"u"?"limit":`${i}.limit`;return this.url.searchParams.set(s,`${t}`),this.url.searchParams.set(o,`${n-t+1}`),this}abortSignal(t){return this.signal=t,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return this.method==="GET"?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain({analyze:t=!1,verbose:n=!1,settings:r=!1,buffers:i=!1,wal:s=!1,format:o="text"}={}){var l;const a=[t?"analyze":null,n?"verbose":null,r?"settings":null,i?"buffers":null,s?"wal":null].filter(Boolean).join("|"),u=(l=this.headers.Accept)!==null&&l!==void 0?l:"application/json";return this.headers.Accept=`application/vnd.pgrst.plan+${o}; for="${u}"; options=${a};`,o==="json"?this:this}rollback(){var t;return((t=this.headers.Prefer)!==null&&t!==void 0?t:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}};Ts.default=Rm;var Im=Le&&Le.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Jr,"__esModule",{value:!0});const $m=Im(Ts);let Lm=class extends $m.default{eq(t,n){return this.url.searchParams.append(t,`eq.${n}`),this}neq(t,n){return this.url.searchParams.append(t,`neq.${n}`),this}gt(t,n){return this.url.searchParams.append(t,`gt.${n}`),this}gte(t,n){return this.url.searchParams.append(t,`gte.${n}`),this}lt(t,n){return this.url.searchParams.append(t,`lt.${n}`),this}lte(t,n){return this.url.searchParams.append(t,`lte.${n}`),this}like(t,n){return this.url.searchParams.append(t,`like.${n}`),this}likeAllOf(t,n){return this.url.searchParams.append(t,`like(all).{${n.join(",")}}`),this}likeAnyOf(t,n){return this.url.searchParams.append(t,`like(any).{${n.join(",")}}`),this}ilike(t,n){return this.url.searchParams.append(t,`ilike.${n}`),this}ilikeAllOf(t,n){return this.url.searchParams.append(t,`ilike(all).{${n.join(",")}}`),this}ilikeAnyOf(t,n){return this.url.searchParams.append(t,`ilike(any).{${n.join(",")}}`),this}is(t,n){return this.url.searchParams.append(t,`is.${n}`),this}in(t,n){const r=Array.from(new Set(n)).map(i=>typeof i=="string"&&new RegExp("[,()]").test(i)?`"${i}"`:`${i}`).join(",");return this.url.searchParams.append(t,`in.(${r})`),this}contains(t,n){return typeof n=="string"?this.url.searchParams.append(t,`cs.${n}`):Array.isArray(n)?this.url.searchParams.append(t,`cs.{${n.join(",")}}`):this.url.searchParams.append(t,`cs.${JSON.stringify(n)}`),this}containedBy(t,n){return typeof n=="string"?this.url.searchParams.append(t,`cd.${n}`):Array.isArray(n)?this.url.searchParams.append(t,`cd.{${n.join(",")}}`):this.url.searchParams.append(t,`cd.${JSON.stringify(n)}`),this}rangeGt(t,n){return this.url.searchParams.append(t,`sr.${n}`),this}rangeGte(t,n){return this.url.searchParams.append(t,`nxl.${n}`),this}rangeLt(t,n){return this.url.searchParams.append(t,`sl.${n}`),this}rangeLte(t,n){return this.url.searchParams.append(t,`nxr.${n}`),this}rangeAdjacent(t,n){return this.url.searchParams.append(t,`adj.${n}`),this}overlaps(t,n){return typeof n=="string"?this.url.searchParams.append(t,`ov.${n}`):this.url.searchParams.append(t,`ov.{${n.join(",")}}`),this}textSearch(t,n,{config:r,type:i}={}){let s="";i==="plain"?s="pl":i==="phrase"?s="ph":i==="websearch"&&(s="w");const o=r===void 0?"":`(${r})`;return this.url.searchParams.append(t,`${s}fts${o}.${n}`),this}match(t){return Object.entries(t).forEach(([n,r])=>{this.url.searchParams.append(n,`eq.${r}`)}),this}not(t,n,r){return this.url.searchParams.append(t,`not.${n}.${r}`),this}or(t,{foreignTable:n,referencedTable:r=n}={}){const i=r?`${r}.or`:"or";return this.url.searchParams.append(i,`(${t})`),this}filter(t,n,r){return this.url.searchParams.append(t,`${n}.${r}`),this}};Jr.default=Lm;var Am=Le&&Le.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Ps,"__esModule",{value:!0});const nr=Am(Jr);let Nm=class{constructor(t,{headers:n={},schema:r,fetch:i}){this.url=t,this.headers=n,this.schema=r,this.fetch=i}select(t,{head:n=!1,count:r}={}){const i=n?"HEAD":"GET";let s=!1;const o=(t??"*").split("").map(l=>/\s/.test(l)&&!s?"":(l==='"'&&(s=!s),l)).join("");return this.url.searchParams.set("select",o),r&&(this.headers.Prefer=`count=${r}`),new nr.default({method:i,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(t,{count:n,defaultToNull:r=!0}={}){const i="POST",s=[];if(this.headers.Prefer&&s.push(this.headers.Prefer),n&&s.push(`count=${n}`),r||s.push("missing=default"),this.headers.Prefer=s.join(","),Array.isArray(t)){const o=t.reduce((l,a)=>l.concat(Object.keys(a)),[]);if(o.length>0){const l=[...new Set(o)].map(a=>`"${a}"`);this.url.searchParams.set("columns",l.join(","))}}return new nr.default({method:i,url:this.url,headers:this.headers,schema:this.schema,body:t,fetch:this.fetch,allowEmpty:!1})}upsert(t,{onConflict:n,ignoreDuplicates:r=!1,count:i,defaultToNull:s=!0}={}){const o="POST",l=[`resolution=${r?"ignore":"merge"}-duplicates`];if(n!==void 0&&this.url.searchParams.set("on_conflict",n),this.headers.Prefer&&l.push(this.headers.Prefer),i&&l.push(`count=${i}`),s||l.push("missing=default"),this.headers.Prefer=l.join(","),Array.isArray(t)){const a=t.reduce((u,c)=>u.concat(Object.keys(c)),[]);if(a.length>0){const u=[...new Set(a)].map(c=>`"${c}"`);this.url.searchParams.set("columns",u.join(","))}}return new nr.default({method:o,url:this.url,headers:this.headers,schema:this.schema,body:t,fetch:this.fetch,allowEmpty:!1})}update(t,{count:n}={}){const r="PATCH",i=[];return this.headers.Prefer&&i.push(this.headers.Prefer),n&&i.push(`count=${n}`),this.headers.Prefer=i.join(","),new nr.default({method:r,url:this.url,headers:this.headers,schema:this.schema,body:t,fetch:this.fetch,allowEmpty:!1})}delete({count:t}={}){const n="DELETE",r=[];return t&&r.push(`count=${t}`),this.headers.Prefer&&r.unshift(this.headers.Prefer),this.headers.Prefer=r.join(","),new nr.default({method:n,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}};Ps.default=Nm;var Rs={},Is={};Object.defineProperty(Is,"__esModule",{value:!0});Is.version=void 0;Is.version="0.0.0-automated";Object.defineProperty(Rs,"__esModule",{value:!0});Rs.DEFAULT_HEADERS=void 0;const bm=Is;Rs.DEFAULT_HEADERS={"X-Client-Info":`postgrest-js/${bm.version}`};var Ff=Le&&Le.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Sa,"__esModule",{value:!0});const Um=Ff(Ps),Dm=Ff(Jr),zm=Rs;let Mm=class Bf{constructor(t,{headers:n={},schema:r,fetch:i}={}){this.url=t,this.headers=Object.assign(Object.assign({},zm.DEFAULT_HEADERS),n),this.schemaName=r,this.fetch=i}from(t){const n=new URL(`${this.url}/${t}`);return new Um.default(n,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}schema(t){return new Bf(this.url,{headers:this.headers,schema:t,fetch:this.fetch})}rpc(t,n={},{head:r=!1,get:i=!1,count:s}={}){let o;const l=new URL(`${this.url}/rpc/${t}`);let a;r||i?(o=r?"HEAD":"GET",Object.entries(n).filter(([c,d])=>d!==void 0).map(([c,d])=>[c,Array.isArray(d)?`{${d.join(",")}}`:`${d}`]).forEach(([c,d])=>{l.searchParams.append(c,d)})):(o="POST",a=n);const u=Object.assign({},this.headers);return s&&(u.Prefer=`count=${s}`),new Dm.default({method:o,url:l,headers:u,schema:this.schemaName,body:a,fetch:this.fetch,allowEmpty:!1})}};Sa.default=Mm;var Hn=Le&&Le.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(ye,"__esModule",{value:!0});ye.PostgrestError=ye.PostgrestBuilder=ye.PostgrestTransformBuilder=ye.PostgrestFilterBuilder=ye.PostgrestQueryBuilder=ye.PostgrestClient=void 0;const Wf=Hn(Sa);ye.PostgrestClient=Wf.default;const Vf=Hn(Ps);ye.PostgrestQueryBuilder=Vf.default;const Hf=Hn(Jr);ye.PostgrestFilterBuilder=Hf.default;const qf=Hn(Ts);ye.PostgrestTransformBuilder=qf.default;const Kf=Hn(js);ye.PostgrestBuilder=Kf.default;const Jf=Hn(Os);ye.PostgrestError=Jf.default;var Fm=ye.default={PostgrestClient:Wf.default,PostgrestQueryBuilder:Vf.default,PostgrestFilterBuilder:Hf.default,PostgrestTransformBuilder:qf.default,PostgrestBuilder:Kf.default,PostgrestError:Jf.default};const{PostgrestClient:Bm,PostgrestQueryBuilder:M0,PostgrestFilterBuilder:F0,PostgrestTransformBuilder:B0,PostgrestBuilder:W0,PostgrestError:V0}=Fm;function Wm(){if(typeof WebSocket<"u")return WebSocket;if(typeof global.WebSocket<"u")return global.WebSocket;if(typeof window.WebSocket<"u")return window.WebSocket;if(typeof self.WebSocket<"u")return self.WebSocket;throw new Error("`WebSocket` is not supported in this environment")}const Vm=Wm(),Hm="2.11.15",qm=`realtime-js/${Hm}`,Km="1.0.0",Qf=1e4,Jm=1e3;var yr;(function(e){e[e.connecting=0]="connecting",e[e.open=1]="open",e[e.closing=2]="closing",e[e.closed=3]="closed"})(yr||(yr={}));var ue;(function(e){e.closed="closed",e.errored="errored",e.joined="joined",e.joining="joining",e.leaving="leaving"})(ue||(ue={}));var Fe;(function(e){e.close="phx_close",e.error="phx_error",e.join="phx_join",e.reply="phx_reply",e.leave="phx_leave",e.access_token="access_token"})(Fe||(Fe={}));var pl;(function(e){e.websocket="websocket"})(pl||(pl={}));var Wt;(function(e){e.Connecting="connecting",e.Open="open",e.Closing="closing",e.Closed="closed"})(Wt||(Wt={}));class Qm{constructor(){this.HEADER_LENGTH=1}decode(t,n){return t.constructor===ArrayBuffer?n(this._binaryDecode(t)):n(typeof t=="string"?JSON.parse(t):{})}_binaryDecode(t){const n=new DataView(t),r=new TextDecoder;return this._decodeBroadcast(t,n,r)}_decodeBroadcast(t,n,r){const i=n.getUint8(1),s=n.getUint8(2);let o=this.HEADER_LENGTH+2;const l=r.decode(t.slice(o,o+i));o=o+i;const a=r.decode(t.slice(o,o+s));o=o+s;const u=JSON.parse(r.decode(t.slice(o,t.byteLength)));return{ref:null,topic:l,event:a,payload:u}}}class Gf{constructor(t,n){this.callback=t,this.timerCalc=n,this.timer=void 0,this.tries=0,this.callback=t,this.timerCalc=n}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}}var D;(function(e){e.abstime="abstime",e.bool="bool",e.date="date",e.daterange="daterange",e.float4="float4",e.float8="float8",e.int2="int2",e.int4="int4",e.int4range="int4range",e.int8="int8",e.int8range="int8range",e.json="json",e.jsonb="jsonb",e.money="money",e.numeric="numeric",e.oid="oid",e.reltime="reltime",e.text="text",e.time="time",e.timestamp="timestamp",e.timestamptz="timestamptz",e.timetz="timetz",e.tsrange="tsrange",e.tstzrange="tstzrange"})(D||(D={}));const Ju=(e,t,n={})=>{var r;const i=(r=n.skipTypes)!==null&&r!==void 0?r:[];return Object.keys(t).reduce((s,o)=>(s[o]=Gm(o,e,t,i),s),{})},Gm=(e,t,n,r)=>{const i=t.find(l=>l.name===e),s=i==null?void 0:i.type,o=n[e];return s&&!r.includes(s)?Yf(s,o):vl(o)},Yf=(e,t)=>{if(e.charAt(0)==="_"){const n=e.slice(1,e.length);return ey(t,n)}switch(e){case D.bool:return Ym(t);case D.float4:case D.float8:case D.int2:case D.int4:case D.int8:case D.numeric:case D.oid:return Xm(t);case D.json:case D.jsonb:return Zm(t);case D.timestamp:return ty(t);case D.abstime:case D.date:case D.daterange:case D.int4range:case D.int8range:case D.money:case D.reltime:case D.text:case D.time:case D.timestamptz:case D.timetz:case D.tsrange:case D.tstzrange:return vl(t);default:return vl(t)}},vl=e=>e,Ym=e=>{switch(e){case"t":return!0;case"f":return!1;default:return e}},Xm=e=>{if(typeof e=="string"){const t=parseFloat(e);if(!Number.isNaN(t))return t}return e},Zm=e=>{if(typeof e=="string")try{return JSON.parse(e)}catch(t){return console.log(`JSON parse error: ${t}`),e}return e},ey=(e,t)=>{if(typeof e!="string")return e;const n=e.length-1,r=e[n];if(e[0]==="{"&&r==="}"){let s;const o=e.slice(1,n);try{s=JSON.parse("["+o+"]")}catch{s=o?o.split(","):[]}return s.map(l=>Yf(t,l))}return e},ty=e=>typeof e=="string"?e.replace(" ","T"):e,Xf=e=>{let t=e;return t=t.replace(/^ws/i,"http"),t=t.replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,""),t.replace(/\/+$/,"")};class co{constructor(t,n,r={},i=Qf){this.channel=t,this.event=n,this.payload=r,this.timeout=i,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(t){this.timeout=t,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(t){this.payload=Object.assign(Object.assign({},this.payload),t)}receive(t,n){var r;return this._hasReceived(t)&&n((r=this.receivedResp)===null||r===void 0?void 0:r.response),this.recHooks.push({status:t,callback:n}),this}startTimeout(){if(this.timeoutTimer)return;this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref);const t=n=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=n,this._matchReceive(n)};this.channel._on(this.refEvent,{},t),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout)}trigger(t,n){this.refEvent&&this.channel._trigger(this.refEvent,{status:t,response:n})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:t,response:n}){this.recHooks.filter(r=>r.status===t).forEach(r=>r.callback(n))}_hasReceived(t){return this.receivedResp&&this.receivedResp.status===t}}var Qu;(function(e){e.SYNC="sync",e.JOIN="join",e.LEAVE="leave"})(Qu||(Qu={}));class wr{constructor(t,n){this.channel=t,this.state={},this.pendingDiffs=[],this.joinRef=null,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};const r=(n==null?void 0:n.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(r.state,{},i=>{const{onJoin:s,onLeave:o,onSync:l}=this.caller;this.joinRef=this.channel._joinRef(),this.state=wr.syncState(this.state,i,s,o),this.pendingDiffs.forEach(a=>{this.state=wr.syncDiff(this.state,a,s,o)}),this.pendingDiffs=[],l()}),this.channel._on(r.diff,{},i=>{const{onJoin:s,onLeave:o,onSync:l}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(i):(this.state=wr.syncDiff(this.state,i,s,o),l())}),this.onJoin((i,s,o)=>{this.channel._trigger("presence",{event:"join",key:i,currentPresences:s,newPresences:o})}),this.onLeave((i,s,o)=>{this.channel._trigger("presence",{event:"leave",key:i,currentPresences:s,leftPresences:o})}),this.onSync(()=>{this.channel._trigger("presence",{event:"sync"})})}static syncState(t,n,r,i){const s=this.cloneDeep(t),o=this.transformState(n),l={},a={};return this.map(s,(u,c)=>{o[u]||(a[u]=c)}),this.map(o,(u,c)=>{const d=s[u];if(d){const f=c.map(_=>_.presence_ref),g=d.map(_=>_.presence_ref),m=c.filter(_=>g.indexOf(_.presence_ref)<0),w=d.filter(_=>f.indexOf(_.presence_ref)<0);m.length>0&&(l[u]=m),w.length>0&&(a[u]=w)}else l[u]=c}),this.syncDiff(s,{joins:l,leaves:a},r,i)}static syncDiff(t,n,r,i){const{joins:s,leaves:o}={joins:this.transformState(n.joins),leaves:this.transformState(n.leaves)};return r||(r=()=>{}),i||(i=()=>{}),this.map(s,(l,a)=>{var u;const c=(u=t[l])!==null&&u!==void 0?u:[];if(t[l]=this.cloneDeep(a),c.length>0){const d=t[l].map(g=>g.presence_ref),f=c.filter(g=>d.indexOf(g.presence_ref)<0);t[l].unshift(...f)}r(l,c,a)}),this.map(o,(l,a)=>{let u=t[l];if(!u)return;const c=a.map(d=>d.presence_ref);u=u.filter(d=>c.indexOf(d.presence_ref)<0),t[l]=u,i(l,u,a),u.length===0&&delete t[l]}),t}static map(t,n){return Object.getOwnPropertyNames(t).map(r=>n(r,t[r]))}static transformState(t){return t=this.cloneDeep(t),Object.getOwnPropertyNames(t).reduce((n,r)=>{const i=t[r];return"metas"in i?n[r]=i.metas.map(s=>(s.presence_ref=s.phx_ref,delete s.phx_ref,delete s.phx_ref_prev,s)):n[r]=i,n},{})}static cloneDeep(t){return JSON.parse(JSON.stringify(t))}onJoin(t){this.caller.onJoin=t}onLeave(t){this.caller.onLeave=t}onSync(t){this.caller.onSync=t}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}var Gu;(function(e){e.ALL="*",e.INSERT="INSERT",e.UPDATE="UPDATE",e.DELETE="DELETE"})(Gu||(Gu={}));var Yu;(function(e){e.BROADCAST="broadcast",e.PRESENCE="presence",e.POSTGRES_CHANGES="postgres_changes",e.SYSTEM="system"})(Yu||(Yu={}));var Ze;(function(e){e.SUBSCRIBED="SUBSCRIBED",e.TIMED_OUT="TIMED_OUT",e.CLOSED="CLOSED",e.CHANNEL_ERROR="CHANNEL_ERROR"})(Ze||(Ze={}));class ka{constructor(t,n={config:{}},r){this.topic=t,this.params=n,this.socket=r,this.bindings={},this.state=ue.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=t.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:""},private:!1},n.config),this.timeout=this.socket.timeout,this.joinPush=new co(this,Fe.join,this.params,this.timeout),this.rejoinTimer=new Gf(()=>this._rejoinUntilConnected(),this.socket.reconnectAfterMs),this.joinPush.receive("ok",()=>{this.state=ue.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(i=>i.send()),this.pushBuffer=[]}),this._onClose(()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=ue.closed,this.socket._remove(this)}),this._onError(i=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,i),this.state=ue.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("timeout",()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=ue.errored,this.rejoinTimer.scheduleTimeout())}),this._on(Fe.reply,{},(i,s)=>{this._trigger(this._replyEventName(s),i)}),this.presence=new wr(this),this.broadcastEndpointURL=Xf(this.socket.endPoint)+"/api/broadcast",this.private=this.params.config.private||!1}subscribe(t,n=this.timeout){var r,i;if(this.socket.isConnected()||this.socket.connect(),this.state==ue.closed){const{config:{broadcast:s,presence:o,private:l}}=this.params;this._onError(c=>t==null?void 0:t(Ze.CHANNEL_ERROR,c)),this._onClose(()=>t==null?void 0:t(Ze.CLOSED));const a={},u={broadcast:s,presence:o,postgres_changes:(i=(r=this.bindings.postgres_changes)===null||r===void 0?void 0:r.map(c=>c.filter))!==null&&i!==void 0?i:[],private:l};this.socket.accessTokenValue&&(a.access_token=this.socket.accessTokenValue),this.updateJoinPayload(Object.assign({config:u},a)),this.joinedOnce=!0,this._rejoin(n),this.joinPush.receive("ok",async({postgres_changes:c})=>{var d;if(this.socket.setAuth(),c===void 0){t==null||t(Ze.SUBSCRIBED);return}else{const f=this.bindings.postgres_changes,g=(d=f==null?void 0:f.length)!==null&&d!==void 0?d:0,m=[];for(let w=0;w<g;w++){const _=f[w],{filter:{event:v,schema:h,table:p,filter:y}}=_,k=c&&c[w];if(k&&k.event===v&&k.schema===h&&k.table===p&&k.filter===y)m.push(Object.assign(Object.assign({},_),{id:k.id}));else{this.unsubscribe(),this.state=ue.errored,t==null||t(Ze.CHANNEL_ERROR,new Error("mismatch between server and client bindings for postgres changes"));return}}this.bindings.postgres_changes=m,t&&t(Ze.SUBSCRIBED);return}}).receive("error",c=>{this.state=ue.errored,t==null||t(Ze.CHANNEL_ERROR,new Error(JSON.stringify(Object.values(c).join(", ")||"error")))}).receive("timeout",()=>{t==null||t(Ze.TIMED_OUT)})}return this}presenceState(){return this.presence.state}async track(t,n={}){return await this.send({type:"presence",event:"track",payload:t},n.timeout||this.timeout)}async untrack(t={}){return await this.send({type:"presence",event:"untrack"},t)}on(t,n,r){return this._on(t,n,r)}async send(t,n={}){var r,i;if(!this._canPush()&&t.type==="broadcast"){const{event:s,payload:o}=t,a={method:"POST",headers:{Authorization:this.socket.accessTokenValue?`Bearer ${this.socket.accessTokenValue}`:"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:s,payload:o,private:this.private}]})};try{const u=await this._fetchWithTimeout(this.broadcastEndpointURL,a,(r=n.timeout)!==null&&r!==void 0?r:this.timeout);return await((i=u.body)===null||i===void 0?void 0:i.cancel()),u.ok?"ok":"error"}catch(u){return u.name==="AbortError"?"timed out":"error"}}else return new Promise(s=>{var o,l,a;const u=this._push(t.type,t,n.timeout||this.timeout);t.type==="broadcast"&&!(!((a=(l=(o=this.params)===null||o===void 0?void 0:o.config)===null||l===void 0?void 0:l.broadcast)===null||a===void 0)&&a.ack)&&s("ok"),u.receive("ok",()=>s("ok")),u.receive("error",()=>s("error")),u.receive("timeout",()=>s("timed out"))})}updateJoinPayload(t){this.joinPush.updatePayload(t)}unsubscribe(t=this.timeout){this.state=ue.leaving;const n=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(Fe.close,"leave",this._joinRef())};this.joinPush.destroy();let r=null;return new Promise(i=>{r=new co(this,Fe.leave,{},t),r.receive("ok",()=>{n(),i("ok")}).receive("timeout",()=>{n(),i("timed out")}).receive("error",()=>{i("error")}),r.send(),this._canPush()||r.trigger("ok",{})}).finally(()=>{r==null||r.destroy()})}teardown(){this.pushBuffer.forEach(t=>t.destroy()),this.rejoinTimer&&clearTimeout(this.rejoinTimer.timer),this.joinPush.destroy()}async _fetchWithTimeout(t,n,r){const i=new AbortController,s=setTimeout(()=>i.abort(),r),o=await this.socket.fetch(t,Object.assign(Object.assign({},n),{signal:i.signal}));return clearTimeout(s),o}_push(t,n,r=this.timeout){if(!this.joinedOnce)throw`tried to push '${t}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let i=new co(this,t,n,r);return this._canPush()?i.send():(i.startTimeout(),this.pushBuffer.push(i)),i}_onMessage(t,n,r){return n}_isMember(t){return this.topic===t}_joinRef(){return this.joinPush.ref}_trigger(t,n,r){var i,s;const o=t.toLocaleLowerCase(),{close:l,error:a,leave:u,join:c}=Fe;if(r&&[l,a,u,c].indexOf(o)>=0&&r!==this._joinRef())return;let f=this._onMessage(o,n,r);if(n&&!f)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(o)?(i=this.bindings.postgres_changes)===null||i===void 0||i.filter(g=>{var m,w,_;return((m=g.filter)===null||m===void 0?void 0:m.event)==="*"||((_=(w=g.filter)===null||w===void 0?void 0:w.event)===null||_===void 0?void 0:_.toLocaleLowerCase())===o}).map(g=>g.callback(f,r)):(s=this.bindings[o])===null||s===void 0||s.filter(g=>{var m,w,_,v,h,p;if(["broadcast","presence","postgres_changes"].includes(o))if("id"in g){const y=g.id,k=(m=g.filter)===null||m===void 0?void 0:m.event;return y&&((w=n.ids)===null||w===void 0?void 0:w.includes(y))&&(k==="*"||(k==null?void 0:k.toLocaleLowerCase())===((_=n.data)===null||_===void 0?void 0:_.type.toLocaleLowerCase()))}else{const y=(h=(v=g==null?void 0:g.filter)===null||v===void 0?void 0:v.event)===null||h===void 0?void 0:h.toLocaleLowerCase();return y==="*"||y===((p=n==null?void 0:n.event)===null||p===void 0?void 0:p.toLocaleLowerCase())}else return g.type.toLocaleLowerCase()===o}).map(g=>{if(typeof f=="object"&&"ids"in f){const m=f.data,{schema:w,table:_,commit_timestamp:v,type:h,errors:p}=m;f=Object.assign(Object.assign({},{schema:w,table:_,commit_timestamp:v,eventType:h,new:{},old:{},errors:p}),this._getPayloadRecords(m))}g.callback(f,r)})}_isClosed(){return this.state===ue.closed}_isJoined(){return this.state===ue.joined}_isJoining(){return this.state===ue.joining}_isLeaving(){return this.state===ue.leaving}_replyEventName(t){return`chan_reply_${t}`}_on(t,n,r){const i=t.toLocaleLowerCase(),s={type:i,filter:n,callback:r};return this.bindings[i]?this.bindings[i].push(s):this.bindings[i]=[s],this}_off(t,n){const r=t.toLocaleLowerCase();return this.bindings[r]=this.bindings[r].filter(i=>{var s;return!(((s=i.type)===null||s===void 0?void 0:s.toLocaleLowerCase())===r&&ka.isEqual(i.filter,n))}),this}static isEqual(t,n){if(Object.keys(t).length!==Object.keys(n).length)return!1;for(const r in t)if(t[r]!==n[r])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(t){this._on(Fe.close,{},t)}_onError(t){this._on(Fe.error,{},n=>t(n))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(t=this.timeout){this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=ue.joining,this.joinPush.resend(t))}_getPayloadRecords(t){const n={new:{},old:{}};return(t.type==="INSERT"||t.type==="UPDATE")&&(n.new=Ju(t.columns,t.record)),(t.type==="UPDATE"||t.type==="DELETE")&&(n.old=Ju(t.columns,t.old_record)),n}}const Xu=()=>{},ny=`
  addEventListener("message", (e) => {
    if (e.data.event === "start") {
      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);
    }
  });`;class ry{constructor(t,n){var r;this.accessTokenValue=null,this.apiKey=null,this.channels=new Array,this.endPoint="",this.httpEndpoint="",this.headers={},this.params={},this.timeout=Qf,this.heartbeatIntervalMs=25e3,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.heartbeatCallback=Xu,this.ref=0,this.logger=Xu,this.conn=null,this.sendBuffer=[],this.serializer=new Qm,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._resolveFetch=s=>{let o;return s?o=s:typeof fetch>"u"?o=(...l)=>Kr(()=>Promise.resolve().then(()=>Vn),void 0).then(({default:a})=>a(...l)):o=fetch,(...l)=>o(...l)},this.endPoint=`${t}/${pl.websocket}`,this.httpEndpoint=Xf(t),n!=null&&n.transport?this.transport=n.transport:this.transport=null,n!=null&&n.params&&(this.params=n.params),n!=null&&n.timeout&&(this.timeout=n.timeout),n!=null&&n.logger&&(this.logger=n.logger),(n!=null&&n.logLevel||n!=null&&n.log_level)&&(this.logLevel=n.logLevel||n.log_level,this.params=Object.assign(Object.assign({},this.params),{log_level:this.logLevel})),n!=null&&n.heartbeatIntervalMs&&(this.heartbeatIntervalMs=n.heartbeatIntervalMs);const i=(r=n==null?void 0:n.params)===null||r===void 0?void 0:r.apikey;if(i&&(this.accessTokenValue=i,this.apiKey=i),this.reconnectAfterMs=n!=null&&n.reconnectAfterMs?n.reconnectAfterMs:s=>[1e3,2e3,5e3,1e4][s-1]||1e4,this.encode=n!=null&&n.encode?n.encode:(s,o)=>o(JSON.stringify(s)),this.decode=n!=null&&n.decode?n.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new Gf(async()=>{this.disconnect(),this.connect()},this.reconnectAfterMs),this.fetch=this._resolveFetch(n==null?void 0:n.fetch),n!=null&&n.worker){if(typeof window<"u"&&!window.Worker)throw new Error("Web Worker is not supported");this.worker=(n==null?void 0:n.worker)||!1,this.workerUrl=n==null?void 0:n.workerUrl}this.accessToken=(n==null?void 0:n.accessToken)||null}connect(){if(!this.conn){if(this.transport||(this.transport=Vm),!this.transport)throw new Error("No transport provided");this.conn=new this.transport(this.endpointURL()),this.setupConnection()}}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:Km}))}disconnect(t,n){this.conn&&(this.conn.onclose=function(){},t?this.conn.close(t,n??""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset(),this.channels.forEach(r=>r.teardown()))}getChannels(){return this.channels}async removeChannel(t){const n=await t.unsubscribe();return this.channels.length===0&&this.disconnect(),n}async removeAllChannels(){const t=await Promise.all(this.channels.map(n=>n.unsubscribe()));return this.channels=[],this.disconnect(),t}log(t,n,r){this.logger(t,n,r)}connectionState(){switch(this.conn&&this.conn.readyState){case yr.connecting:return Wt.Connecting;case yr.open:return Wt.Open;case yr.closing:return Wt.Closing;default:return Wt.Closed}}isConnected(){return this.connectionState()===Wt.Open}channel(t,n={config:{}}){const r=`realtime:${t}`,i=this.getChannels().find(s=>s.topic===r);if(i)return i;{const s=new ka(`realtime:${t}`,n,this);return this.channels.push(s),s}}push(t){const{topic:n,event:r,payload:i,ref:s}=t,o=()=>{this.encode(t,l=>{var a;(a=this.conn)===null||a===void 0||a.send(l)})};this.log("push",`${n} ${r} (${s})`,i),this.isConnected()?o():this.sendBuffer.push(o)}async setAuth(t=null){let n=t||this.accessToken&&await this.accessToken()||this.accessTokenValue;this.accessTokenValue!=n&&(this.accessTokenValue=n,this.channels.forEach(r=>{const i={access_token:n,version:qm};n&&r.updateJoinPayload(i),r.joinedOnce&&r._isJoined()&&r._push(Fe.access_token,{access_token:n})}))}async sendHeartbeat(){var t;if(!this.isConnected()){this.heartbeatCallback("disconnected");return}if(this.pendingHeartbeatRef){this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),this.heartbeatCallback("timeout"),(t=this.conn)===null||t===void 0||t.close(Jm,"hearbeat timeout");return}this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.heartbeatCallback("sent"),await this.setAuth()}onHeartbeat(t){this.heartbeatCallback=t}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(t=>t()),this.sendBuffer=[])}_makeRef(){let t=this.ref+1;return t===this.ref?this.ref=0:this.ref=t,this.ref.toString()}_leaveOpenTopic(t){let n=this.channels.find(r=>r.topic===t&&(r._isJoined()||r._isJoining()));n&&(this.log("transport",`leaving duplicate topic "${t}"`),n.unsubscribe())}_remove(t){this.channels=this.channels.filter(n=>n.topic!==t.topic)}setupConnection(){this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=t=>this._onConnError(t),this.conn.onmessage=t=>this._onConnMessage(t),this.conn.onclose=t=>this._onConnClose(t))}_onConnMessage(t){this.decode(t.data,n=>{let{topic:r,event:i,payload:s,ref:o}=n;r==="phoenix"&&i==="phx_reply"&&this.heartbeatCallback(n.payload.status=="ok"?"ok":"error"),o&&o===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null),this.log("receive",`${s.status||""} ${r} ${i} ${o&&"("+o+")"||""}`,s),Array.from(this.channels).filter(l=>l._isMember(r)).forEach(l=>l._trigger(i,s,o)),this.stateChangeCallbacks.message.forEach(l=>l(n))})}_onConnOpen(){this.log("transport",`connected to ${this.endpointURL()}`),this.flushSendBuffer(),this.reconnectTimer.reset(),this.worker?this.workerRef||this._startWorkerHeartbeat():this._startHeartbeat(),this.stateChangeCallbacks.open.forEach(t=>t())}_startHeartbeat(){this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>this.sendHeartbeat(),this.heartbeatIntervalMs)}_startWorkerHeartbeat(){this.workerUrl?this.log("worker",`starting worker for from ${this.workerUrl}`):this.log("worker","starting default worker");const t=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(t),this.workerRef.onerror=n=>{this.log("worker","worker error",n.message),this.workerRef.terminate()},this.workerRef.onmessage=n=>{n.data.event==="keepAlive"&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}_onConnClose(t){this.log("transport","close",t),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(n=>n(t))}_onConnError(t){this.log("transport",`${t}`),this._triggerChanError(),this.stateChangeCallbacks.error.forEach(n=>n(t))}_triggerChanError(){this.channels.forEach(t=>t._trigger(Fe.error))}_appendParams(t,n){if(Object.keys(n).length===0)return t;const r=t.match(/\?/)?"&":"?",i=new URLSearchParams(n);return`${t}${r}${i}`}_workerObjectUrl(t){let n;if(t)n=t;else{const r=new Blob([ny],{type:"application/javascript"});n=URL.createObjectURL(r)}return n}}class Ea extends Error{constructor(t){super(t),this.__isStorageError=!0,this.name="StorageError"}}function te(e){return typeof e=="object"&&e!==null&&"__isStorageError"in e}class iy extends Ea{constructor(t,n){super(t),this.name="StorageApiError",this.status=n}toJSON(){return{name:this.name,message:this.message,status:this.status}}}class gl extends Ea{constructor(t,n){super(t),this.name="StorageUnknownError",this.originalError=n}}var sy=globalThis&&globalThis.__awaiter||function(e,t,n,r){function i(s){return s instanceof n?s:new n(function(o){o(s)})}return new(n||(n=Promise))(function(s,o){function l(c){try{u(r.next(c))}catch(d){o(d)}}function a(c){try{u(r.throw(c))}catch(d){o(d)}}function u(c){c.done?s(c.value):i(c.value).then(l,a)}u((r=r.apply(e,t||[])).next())})};const Zf=e=>{let t;return e?t=e:typeof fetch>"u"?t=(...n)=>Kr(()=>Promise.resolve().then(()=>Vn),void 0).then(({default:r})=>r(...n)):t=fetch,(...n)=>t(...n)},oy=()=>sy(void 0,void 0,void 0,function*(){return typeof Response>"u"?(yield Kr(()=>Promise.resolve().then(()=>Vn),void 0)).Response:Response}),ml=e=>{if(Array.isArray(e))return e.map(n=>ml(n));if(typeof e=="function"||e!==Object(e))return e;const t={};return Object.entries(e).forEach(([n,r])=>{const i=n.replace(/([-_][a-z])/gi,s=>s.toUpperCase().replace(/[-_]/g,""));t[i]=ml(r)}),t};var nn=globalThis&&globalThis.__awaiter||function(e,t,n,r){function i(s){return s instanceof n?s:new n(function(o){o(s)})}return new(n||(n=Promise))(function(s,o){function l(c){try{u(r.next(c))}catch(d){o(d)}}function a(c){try{u(r.throw(c))}catch(d){o(d)}}function u(c){c.done?s(c.value):i(c.value).then(l,a)}u((r=r.apply(e,t||[])).next())})};const fo=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),ly=(e,t,n)=>nn(void 0,void 0,void 0,function*(){const r=yield oy();e instanceof r&&!(n!=null&&n.noResolveJson)?e.json().then(i=>{t(new iy(fo(i),e.status||500))}).catch(i=>{t(new gl(fo(i),i))}):t(new gl(fo(e),e))}),ay=(e,t,n,r)=>{const i={method:e,headers:(t==null?void 0:t.headers)||{}};return e==="GET"?i:(i.headers=Object.assign({"Content-Type":"application/json"},t==null?void 0:t.headers),r&&(i.body=JSON.stringify(r)),Object.assign(Object.assign({},i),n))};function Qr(e,t,n,r,i,s){return nn(this,void 0,void 0,function*(){return new Promise((o,l)=>{e(n,ay(t,r,i,s)).then(a=>{if(!a.ok)throw a;return r!=null&&r.noResolveJson?a:a.json()}).then(a=>o(a)).catch(a=>ly(a,l,r))})})}function ss(e,t,n,r){return nn(this,void 0,void 0,function*(){return Qr(e,"GET",t,n,r)})}function ht(e,t,n,r,i){return nn(this,void 0,void 0,function*(){return Qr(e,"POST",t,r,i,n)})}function uy(e,t,n,r,i){return nn(this,void 0,void 0,function*(){return Qr(e,"PUT",t,r,i,n)})}function cy(e,t,n,r){return nn(this,void 0,void 0,function*(){return Qr(e,"HEAD",t,Object.assign(Object.assign({},n),{noResolveJson:!0}),r)})}function eh(e,t,n,r,i){return nn(this,void 0,void 0,function*(){return Qr(e,"DELETE",t,r,i,n)})}var ge=globalThis&&globalThis.__awaiter||function(e,t,n,r){function i(s){return s instanceof n?s:new n(function(o){o(s)})}return new(n||(n=Promise))(function(s,o){function l(c){try{u(r.next(c))}catch(d){o(d)}}function a(c){try{u(r.throw(c))}catch(d){o(d)}}function u(c){c.done?s(c.value):i(c.value).then(l,a)}u((r=r.apply(e,t||[])).next())})};const dy={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},Zu={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class fy{constructor(t,n={},r,i){this.url=t,this.headers=n,this.bucketId=r,this.fetch=Zf(i)}uploadOrUpdate(t,n,r,i){return ge(this,void 0,void 0,function*(){try{let s;const o=Object.assign(Object.assign({},Zu),i);let l=Object.assign(Object.assign({},this.headers),t==="POST"&&{"x-upsert":String(o.upsert)});const a=o.metadata;typeof Blob<"u"&&r instanceof Blob?(s=new FormData,s.append("cacheControl",o.cacheControl),a&&s.append("metadata",this.encodeMetadata(a)),s.append("",r)):typeof FormData<"u"&&r instanceof FormData?(s=r,s.append("cacheControl",o.cacheControl),a&&s.append("metadata",this.encodeMetadata(a))):(s=r,l["cache-control"]=`max-age=${o.cacheControl}`,l["content-type"]=o.contentType,a&&(l["x-metadata"]=this.toBase64(this.encodeMetadata(a)))),i!=null&&i.headers&&(l=Object.assign(Object.assign({},l),i.headers));const u=this._removeEmptyFolders(n),c=this._getFinalPath(u),d=yield this.fetch(`${this.url}/object/${c}`,Object.assign({method:t,body:s,headers:l},o!=null&&o.duplex?{duplex:o.duplex}:{})),f=yield d.json();return d.ok?{data:{path:u,id:f.Id,fullPath:f.Key},error:null}:{data:null,error:f}}catch(s){if(te(s))return{data:null,error:s};throw s}})}upload(t,n,r){return ge(this,void 0,void 0,function*(){return this.uploadOrUpdate("POST",t,n,r)})}uploadToSignedUrl(t,n,r,i){return ge(this,void 0,void 0,function*(){const s=this._removeEmptyFolders(t),o=this._getFinalPath(s),l=new URL(this.url+`/object/upload/sign/${o}`);l.searchParams.set("token",n);try{let a;const u=Object.assign({upsert:Zu.upsert},i),c=Object.assign(Object.assign({},this.headers),{"x-upsert":String(u.upsert)});typeof Blob<"u"&&r instanceof Blob?(a=new FormData,a.append("cacheControl",u.cacheControl),a.append("",r)):typeof FormData<"u"&&r instanceof FormData?(a=r,a.append("cacheControl",u.cacheControl)):(a=r,c["cache-control"]=`max-age=${u.cacheControl}`,c["content-type"]=u.contentType);const d=yield this.fetch(l.toString(),{method:"PUT",body:a,headers:c}),f=yield d.json();return d.ok?{data:{path:s,fullPath:f.Key},error:null}:{data:null,error:f}}catch(a){if(te(a))return{data:null,error:a};throw a}})}createSignedUploadUrl(t,n){return ge(this,void 0,void 0,function*(){try{let r=this._getFinalPath(t);const i=Object.assign({},this.headers);n!=null&&n.upsert&&(i["x-upsert"]="true");const s=yield ht(this.fetch,`${this.url}/object/upload/sign/${r}`,{},{headers:i}),o=new URL(this.url+s.url),l=o.searchParams.get("token");if(!l)throw new Ea("No token returned by API");return{data:{signedUrl:o.toString(),path:t,token:l},error:null}}catch(r){if(te(r))return{data:null,error:r};throw r}})}update(t,n,r){return ge(this,void 0,void 0,function*(){return this.uploadOrUpdate("PUT",t,n,r)})}move(t,n,r){return ge(this,void 0,void 0,function*(){try{return{data:yield ht(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:t,destinationKey:n,destinationBucket:r==null?void 0:r.destinationBucket},{headers:this.headers}),error:null}}catch(i){if(te(i))return{data:null,error:i};throw i}})}copy(t,n,r){return ge(this,void 0,void 0,function*(){try{return{data:{path:(yield ht(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:t,destinationKey:n,destinationBucket:r==null?void 0:r.destinationBucket},{headers:this.headers})).Key},error:null}}catch(i){if(te(i))return{data:null,error:i};throw i}})}createSignedUrl(t,n,r){return ge(this,void 0,void 0,function*(){try{let i=this._getFinalPath(t),s=yield ht(this.fetch,`${this.url}/object/sign/${i}`,Object.assign({expiresIn:n},r!=null&&r.transform?{transform:r.transform}:{}),{headers:this.headers});const o=r!=null&&r.download?`&download=${r.download===!0?"":r.download}`:"";return s={signedUrl:encodeURI(`${this.url}${s.signedURL}${o}`)},{data:s,error:null}}catch(i){if(te(i))return{data:null,error:i};throw i}})}createSignedUrls(t,n,r){return ge(this,void 0,void 0,function*(){try{const i=yield ht(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:n,paths:t},{headers:this.headers}),s=r!=null&&r.download?`&download=${r.download===!0?"":r.download}`:"";return{data:i.map(o=>Object.assign(Object.assign({},o),{signedUrl:o.signedURL?encodeURI(`${this.url}${o.signedURL}${s}`):null})),error:null}}catch(i){if(te(i))return{data:null,error:i};throw i}})}download(t,n){return ge(this,void 0,void 0,function*(){const i=typeof(n==null?void 0:n.transform)<"u"?"render/image/authenticated":"object",s=this.transformOptsToQueryString((n==null?void 0:n.transform)||{}),o=s?`?${s}`:"";try{const l=this._getFinalPath(t);return{data:yield(yield ss(this.fetch,`${this.url}/${i}/${l}${o}`,{headers:this.headers,noResolveJson:!0})).blob(),error:null}}catch(l){if(te(l))return{data:null,error:l};throw l}})}info(t){return ge(this,void 0,void 0,function*(){const n=this._getFinalPath(t);try{const r=yield ss(this.fetch,`${this.url}/object/info/${n}`,{headers:this.headers});return{data:ml(r),error:null}}catch(r){if(te(r))return{data:null,error:r};throw r}})}exists(t){return ge(this,void 0,void 0,function*(){const n=this._getFinalPath(t);try{return yield cy(this.fetch,`${this.url}/object/${n}`,{headers:this.headers}),{data:!0,error:null}}catch(r){if(te(r)&&r instanceof gl){const i=r.originalError;if([400,404].includes(i==null?void 0:i.status))return{data:!1,error:r}}throw r}})}getPublicUrl(t,n){const r=this._getFinalPath(t),i=[],s=n!=null&&n.download?`download=${n.download===!0?"":n.download}`:"";s!==""&&i.push(s);const l=typeof(n==null?void 0:n.transform)<"u"?"render/image":"object",a=this.transformOptsToQueryString((n==null?void 0:n.transform)||{});a!==""&&i.push(a);let u=i.join("&");return u!==""&&(u=`?${u}`),{data:{publicUrl:encodeURI(`${this.url}/${l}/public/${r}${u}`)}}}remove(t){return ge(this,void 0,void 0,function*(){try{return{data:yield eh(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:t},{headers:this.headers}),error:null}}catch(n){if(te(n))return{data:null,error:n};throw n}})}list(t,n,r){return ge(this,void 0,void 0,function*(){try{const i=Object.assign(Object.assign(Object.assign({},dy),n),{prefix:t||""});return{data:yield ht(this.fetch,`${this.url}/object/list/${this.bucketId}`,i,{headers:this.headers},r),error:null}}catch(i){if(te(i))return{data:null,error:i};throw i}})}encodeMetadata(t){return JSON.stringify(t)}toBase64(t){return typeof Buffer<"u"?Buffer.from(t).toString("base64"):btoa(t)}_getFinalPath(t){return`${this.bucketId}/${t}`}_removeEmptyFolders(t){return t.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(t){const n=[];return t.width&&n.push(`width=${t.width}`),t.height&&n.push(`height=${t.height}`),t.resize&&n.push(`resize=${t.resize}`),t.format&&n.push(`format=${t.format}`),t.quality&&n.push(`quality=${t.quality}`),n.join("&")}}const hy="2.7.1",py={"X-Client-Info":`storage-js/${hy}`};var sn=globalThis&&globalThis.__awaiter||function(e,t,n,r){function i(s){return s instanceof n?s:new n(function(o){o(s)})}return new(n||(n=Promise))(function(s,o){function l(c){try{u(r.next(c))}catch(d){o(d)}}function a(c){try{u(r.throw(c))}catch(d){o(d)}}function u(c){c.done?s(c.value):i(c.value).then(l,a)}u((r=r.apply(e,t||[])).next())})};class vy{constructor(t,n={},r){this.url=t,this.headers=Object.assign(Object.assign({},py),n),this.fetch=Zf(r)}listBuckets(){return sn(this,void 0,void 0,function*(){try{return{data:yield ss(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(t){if(te(t))return{data:null,error:t};throw t}})}getBucket(t){return sn(this,void 0,void 0,function*(){try{return{data:yield ss(this.fetch,`${this.url}/bucket/${t}`,{headers:this.headers}),error:null}}catch(n){if(te(n))return{data:null,error:n};throw n}})}createBucket(t,n={public:!1}){return sn(this,void 0,void 0,function*(){try{return{data:yield ht(this.fetch,`${this.url}/bucket`,{id:t,name:t,public:n.public,file_size_limit:n.fileSizeLimit,allowed_mime_types:n.allowedMimeTypes},{headers:this.headers}),error:null}}catch(r){if(te(r))return{data:null,error:r};throw r}})}updateBucket(t,n){return sn(this,void 0,void 0,function*(){try{return{data:yield uy(this.fetch,`${this.url}/bucket/${t}`,{id:t,name:t,public:n.public,file_size_limit:n.fileSizeLimit,allowed_mime_types:n.allowedMimeTypes},{headers:this.headers}),error:null}}catch(r){if(te(r))return{data:null,error:r};throw r}})}emptyBucket(t){return sn(this,void 0,void 0,function*(){try{return{data:yield ht(this.fetch,`${this.url}/bucket/${t}/empty`,{},{headers:this.headers}),error:null}}catch(n){if(te(n))return{data:null,error:n};throw n}})}deleteBucket(t){return sn(this,void 0,void 0,function*(){try{return{data:yield eh(this.fetch,`${this.url}/bucket/${t}`,{},{headers:this.headers}),error:null}}catch(n){if(te(n))return{data:null,error:n};throw n}})}}class gy extends vy{constructor(t,n={},r){super(t,n,r)}from(t){return new fy(this.url,this.headers,t,this.fetch)}}const my="2.50.2";let ar="";typeof Deno<"u"?ar="deno":typeof document<"u"?ar="web":typeof navigator<"u"&&navigator.product==="ReactNative"?ar="react-native":ar="node";const yy={"X-Client-Info":`supabase-js-${ar}/${my}`},wy={headers:yy},_y={schema:"public"},Sy={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},ky={};var Ey=globalThis&&globalThis.__awaiter||function(e,t,n,r){function i(s){return s instanceof n?s:new n(function(o){o(s)})}return new(n||(n=Promise))(function(s,o){function l(c){try{u(r.next(c))}catch(d){o(d)}}function a(c){try{u(r.throw(c))}catch(d){o(d)}}function u(c){c.done?s(c.value):i(c.value).then(l,a)}u((r=r.apply(e,t||[])).next())})};const xy=e=>{let t;return e?t=e:typeof fetch>"u"?t=Df:t=fetch,(...n)=>t(...n)},Cy=()=>typeof Headers>"u"?zf:Headers,Py=(e,t,n)=>{const r=xy(n),i=Cy();return(s,o)=>Ey(void 0,void 0,void 0,function*(){var l;const a=(l=yield t())!==null&&l!==void 0?l:e;let u=new i(o==null?void 0:o.headers);return u.has("apikey")||u.set("apikey",e),u.has("Authorization")||u.set("Authorization",`Bearer ${a}`),r(s,Object.assign(Object.assign({},o),{headers:u}))})};var Ty=globalThis&&globalThis.__awaiter||function(e,t,n,r){function i(s){return s instanceof n?s:new n(function(o){o(s)})}return new(n||(n=Promise))(function(s,o){function l(c){try{u(r.next(c))}catch(d){o(d)}}function a(c){try{u(r.throw(c))}catch(d){o(d)}}function u(c){c.done?s(c.value):i(c.value).then(l,a)}u((r=r.apply(e,t||[])).next())})};function jy(e){return e.endsWith("/")?e:e+"/"}function Oy(e,t){var n,r;const{db:i,auth:s,realtime:o,global:l}=e,{db:a,auth:u,realtime:c,global:d}=t,f={db:Object.assign(Object.assign({},a),i),auth:Object.assign(Object.assign({},u),s),realtime:Object.assign(Object.assign({},c),o),global:Object.assign(Object.assign(Object.assign({},d),l),{headers:Object.assign(Object.assign({},(n=d==null?void 0:d.headers)!==null&&n!==void 0?n:{}),(r=l==null?void 0:l.headers)!==null&&r!==void 0?r:{})}),accessToken:()=>Ty(this,void 0,void 0,function*(){return""})};return e.accessToken?f.accessToken=e.accessToken:delete f.accessToken,f}const th="2.70.0",un=30*1e3,yl=3,ho=yl*un,Ry="http://localhost:9999",Iy="supabase.auth.token",$y={"X-Client-Info":`gotrue-js/${th}`},wl="X-Supabase-Api-Version",nh={"2024-01-01":{timestamp:Date.parse("2024-01-01T00:00:00.0Z"),name:"2024-01-01"}},Ly=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i,Ay=6e5;class xa extends Error{constructor(t,n,r){super(t),this.__isAuthError=!0,this.name="AuthError",this.status=n,this.code=r}}function R(e){return typeof e=="object"&&e!==null&&"__isAuthError"in e}class Ny extends xa{constructor(t,n,r){super(t,n,r),this.name="AuthApiError",this.status=n,this.code=r}}function by(e){return R(e)&&e.name==="AuthApiError"}class rh extends xa{constructor(t,n){super(t),this.name="AuthUnknownError",this.originalError=n}}class bt extends xa{constructor(t,n,r,i){super(t,r,i),this.name=n,this.status=r}}class ct extends bt{constructor(){super("Auth session missing!","AuthSessionMissingError",400,void 0)}}function Uy(e){return R(e)&&e.name==="AuthSessionMissingError"}class vi extends bt{constructor(){super("Auth session or user missing","AuthInvalidTokenResponseError",500,void 0)}}class gi extends bt{constructor(t){super(t,"AuthInvalidCredentialsError",400,void 0)}}class mi extends bt{constructor(t,n=null){super(t,"AuthImplicitGrantRedirectError",500,void 0),this.details=null,this.details=n}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}function Dy(e){return R(e)&&e.name==="AuthImplicitGrantRedirectError"}class ec extends bt{constructor(t,n=null){super(t,"AuthPKCEGrantCodeExchangeError",500,void 0),this.details=null,this.details=n}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class _l extends bt{constructor(t,n){super(t,"AuthRetryableFetchError",n,void 0)}}function po(e){return R(e)&&e.name==="AuthRetryableFetchError"}class tc extends bt{constructor(t,n,r){super(t,"AuthWeakPasswordError",n,"weak_password"),this.reasons=r}}class _r extends bt{constructor(t){super(t,"AuthInvalidJwtError",400,"invalid_jwt")}}const os="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),nc=` 	
\r=`.split(""),zy=(()=>{const e=new Array(128);for(let t=0;t<e.length;t+=1)e[t]=-1;for(let t=0;t<nc.length;t+=1)e[nc[t].charCodeAt(0)]=-2;for(let t=0;t<os.length;t+=1)e[os[t].charCodeAt(0)]=t;return e})();function rc(e,t,n){if(e!==null)for(t.queue=t.queue<<8|e,t.queuedBits+=8;t.queuedBits>=6;){const r=t.queue>>t.queuedBits-6&63;n(os[r]),t.queuedBits-=6}else if(t.queuedBits>0)for(t.queue=t.queue<<6-t.queuedBits,t.queuedBits=6;t.queuedBits>=6;){const r=t.queue>>t.queuedBits-6&63;n(os[r]),t.queuedBits-=6}}function ih(e,t,n){const r=zy[e];if(r>-1)for(t.queue=t.queue<<6|r,t.queuedBits+=6;t.queuedBits>=8;)n(t.queue>>t.queuedBits-8&255),t.queuedBits-=8;else{if(r===-2)return;throw new Error(`Invalid Base64-URL character "${String.fromCharCode(e)}"`)}}function ic(e){const t=[],n=o=>{t.push(String.fromCodePoint(o))},r={utf8seq:0,codepoint:0},i={queue:0,queuedBits:0},s=o=>{By(o,r,n)};for(let o=0;o<e.length;o+=1)ih(e.charCodeAt(o),i,s);return t.join("")}function My(e,t){if(e<=127){t(e);return}else if(e<=2047){t(192|e>>6),t(128|e&63);return}else if(e<=65535){t(224|e>>12),t(128|e>>6&63),t(128|e&63);return}else if(e<=1114111){t(240|e>>18),t(128|e>>12&63),t(128|e>>6&63),t(128|e&63);return}throw new Error(`Unrecognized Unicode codepoint: ${e.toString(16)}`)}function Fy(e,t){for(let n=0;n<e.length;n+=1){let r=e.charCodeAt(n);if(r>55295&&r<=56319){const i=(r-55296)*1024&65535;r=(e.charCodeAt(n+1)-56320&65535|i)+65536,n+=1}My(r,t)}}function By(e,t,n){if(t.utf8seq===0){if(e<=127){n(e);return}for(let r=1;r<6;r+=1)if(!(e>>7-r&1)){t.utf8seq=r;break}if(t.utf8seq===2)t.codepoint=e&31;else if(t.utf8seq===3)t.codepoint=e&15;else if(t.utf8seq===4)t.codepoint=e&7;else throw new Error("Invalid UTF-8 sequence");t.utf8seq-=1}else if(t.utf8seq>0){if(e<=127)throw new Error("Invalid UTF-8 sequence");t.codepoint=t.codepoint<<6|e&63,t.utf8seq-=1,t.utf8seq===0&&n(t.codepoint)}}function Wy(e){const t=[],n={queue:0,queuedBits:0},r=i=>{t.push(i)};for(let i=0;i<e.length;i+=1)ih(e.charCodeAt(i),n,r);return new Uint8Array(t)}function Vy(e){const t=[];return Fy(e,n=>t.push(n)),new Uint8Array(t)}function Hy(e){const t=[],n={queue:0,queuedBits:0},r=i=>{t.push(i)};return e.forEach(i=>rc(i,n,r)),rc(null,n,r),t.join("")}function qy(e){return Math.round(Date.now()/1e3)+e}function Ky(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){const t=Math.random()*16|0;return(e=="x"?t:t&3|8).toString(16)})}const De=()=>typeof window<"u"&&typeof document<"u",zt={tested:!1,writable:!1},Sr=()=>{if(!De())return!1;try{if(typeof globalThis.localStorage!="object")return!1}catch{return!1}if(zt.tested)return zt.writable;const e=`lswt-${Math.random()}${Math.random()}`;try{globalThis.localStorage.setItem(e,e),globalThis.localStorage.removeItem(e),zt.tested=!0,zt.writable=!0}catch{zt.tested=!0,zt.writable=!1}return zt.writable};function Jy(e){const t={},n=new URL(e);if(n.hash&&n.hash[0]==="#")try{new URLSearchParams(n.hash.substring(1)).forEach((i,s)=>{t[s]=i})}catch{}return n.searchParams.forEach((r,i)=>{t[i]=r}),t}const sh=e=>{let t;return e?t=e:typeof fetch>"u"?t=(...n)=>Kr(()=>Promise.resolve().then(()=>Vn),void 0).then(({default:r})=>r(...n)):t=fetch,(...n)=>t(...n)},Qy=e=>typeof e=="object"&&e!==null&&"status"in e&&"ok"in e&&"json"in e&&typeof e.json=="function",oh=async(e,t,n)=>{await e.setItem(t,JSON.stringify(n))},yi=async(e,t)=>{const n=await e.getItem(t);if(!n)return null;try{return JSON.parse(n)}catch{return n}},wi=async(e,t)=>{await e.removeItem(t)};class $s{constructor(){this.promise=new $s.promiseConstructor((t,n)=>{this.resolve=t,this.reject=n})}}$s.promiseConstructor=Promise;function vo(e){const t=e.split(".");if(t.length!==3)throw new _r("Invalid JWT structure");for(let r=0;r<t.length;r++)if(!Ly.test(t[r]))throw new _r("JWT not in base64url format");return{header:JSON.parse(ic(t[0])),payload:JSON.parse(ic(t[1])),signature:Wy(t[2]),raw:{header:t[0],payload:t[1]}}}async function Gy(e){return await new Promise(t=>{setTimeout(()=>t(null),e)})}function Yy(e,t){return new Promise((r,i)=>{(async()=>{for(let s=0;s<1/0;s++)try{const o=await e(s);if(!t(s,null,o)){r(o);return}}catch(o){if(!t(s,o)){i(o);return}}})()})}function Xy(e){return("0"+e.toString(16)).substr(-2)}function Zy(){const t=new Uint32Array(56);if(typeof crypto>"u"){const n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",r=n.length;let i="";for(let s=0;s<56;s++)i+=n.charAt(Math.floor(Math.random()*r));return i}return crypto.getRandomValues(t),Array.from(t,Xy).join("")}async function e0(e){const n=new TextEncoder().encode(e),r=await crypto.subtle.digest("SHA-256",n),i=new Uint8Array(r);return Array.from(i).map(s=>String.fromCharCode(s)).join("")}async function t0(e){if(!(typeof crypto<"u"&&typeof crypto.subtle<"u"&&typeof TextEncoder<"u"))return console.warn("WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256."),e;const n=await e0(e);return btoa(n).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}async function on(e,t,n=!1){const r=Zy();let i=r;n&&(i+="/PASSWORD_RECOVERY"),await oh(e,`${t}-code-verifier`,i);const s=await t0(r);return[s,r===s?"plain":"s256"]}const n0=/^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i;function r0(e){const t=e.headers.get(wl);if(!t||!t.match(n0))return null;try{return new Date(`${t}T00:00:00.0Z`)}catch{return null}}function i0(e){if(!e)throw new Error("Missing exp claim");const t=Math.floor(Date.now()/1e3);if(e<=t)throw new Error("JWT has expired")}function s0(e){switch(e){case"RS256":return{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}};case"ES256":return{name:"ECDSA",namedCurve:"P-256",hash:{name:"SHA-256"}};default:throw new Error("Invalid alg claim")}}const o0=/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/;function ln(e){if(!o0.test(e))throw new Error("@supabase/auth-js: Expected parameter to be UUID but is not")}var l0=globalThis&&globalThis.__rest||function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,r=Object.getOwnPropertySymbols(e);i<r.length;i++)t.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(n[r[i]]=e[r[i]]);return n};const Bt=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),a0=[502,503,504];async function sc(e){var t;if(!Qy(e))throw new _l(Bt(e),0);if(a0.includes(e.status))throw new _l(Bt(e),e.status);let n;try{n=await e.json()}catch(s){throw new rh(Bt(s),s)}let r;const i=r0(e);if(i&&i.getTime()>=nh["2024-01-01"].timestamp&&typeof n=="object"&&n&&typeof n.code=="string"?r=n.code:typeof n=="object"&&n&&typeof n.error_code=="string"&&(r=n.error_code),r){if(r==="weak_password")throw new tc(Bt(n),e.status,((t=n.weak_password)===null||t===void 0?void 0:t.reasons)||[]);if(r==="session_not_found")throw new ct}else if(typeof n=="object"&&n&&typeof n.weak_password=="object"&&n.weak_password&&Array.isArray(n.weak_password.reasons)&&n.weak_password.reasons.length&&n.weak_password.reasons.reduce((s,o)=>s&&typeof o=="string",!0))throw new tc(Bt(n),e.status,n.weak_password.reasons);throw new Ny(Bt(n),e.status||500,r)}const u0=(e,t,n,r)=>{const i={method:e,headers:(t==null?void 0:t.headers)||{}};return e==="GET"?i:(i.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},t==null?void 0:t.headers),i.body=JSON.stringify(r),Object.assign(Object.assign({},i),n))};async function L(e,t,n,r){var i;const s=Object.assign({},r==null?void 0:r.headers);s[wl]||(s[wl]=nh["2024-01-01"].name),r!=null&&r.jwt&&(s.Authorization=`Bearer ${r.jwt}`);const o=(i=r==null?void 0:r.query)!==null&&i!==void 0?i:{};r!=null&&r.redirectTo&&(o.redirect_to=r.redirectTo);const l=Object.keys(o).length?"?"+new URLSearchParams(o).toString():"",a=await c0(e,t,n+l,{headers:s,noResolveJson:r==null?void 0:r.noResolveJson},{},r==null?void 0:r.body);return r!=null&&r.xform?r==null?void 0:r.xform(a):{data:Object.assign({},a),error:null}}async function c0(e,t,n,r,i,s){const o=u0(t,r,i,s);let l;try{l=await e(n,Object.assign({},o))}catch(a){throw console.error(a),new _l(Bt(a),0)}if(l.ok||await sc(l),r!=null&&r.noResolveJson)return l;try{return await l.json()}catch(a){await sc(a)}}function Ye(e){var t;let n=null;p0(e)&&(n=Object.assign({},e),e.expires_at||(n.expires_at=qy(e.expires_in)));const r=(t=e.user)!==null&&t!==void 0?t:e;return{data:{session:n,user:r},error:null}}function oc(e){const t=Ye(e);return!t.error&&e.weak_password&&typeof e.weak_password=="object"&&Array.isArray(e.weak_password.reasons)&&e.weak_password.reasons.length&&e.weak_password.message&&typeof e.weak_password.message=="string"&&e.weak_password.reasons.reduce((n,r)=>n&&typeof r=="string",!0)&&(t.data.weak_password=e.weak_password),t}function gt(e){var t;return{data:{user:(t=e.user)!==null&&t!==void 0?t:e},error:null}}function d0(e){return{data:e,error:null}}function f0(e){const{action_link:t,email_otp:n,hashed_token:r,redirect_to:i,verification_type:s}=e,o=l0(e,["action_link","email_otp","hashed_token","redirect_to","verification_type"]),l={action_link:t,email_otp:n,hashed_token:r,redirect_to:i,verification_type:s},a=Object.assign({},o);return{data:{properties:l,user:a},error:null}}function h0(e){return e}function p0(e){return e.access_token&&e.refresh_token&&e.expires_in}const go=["global","local","others"];var v0=globalThis&&globalThis.__rest||function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,r=Object.getOwnPropertySymbols(e);i<r.length;i++)t.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(n[r[i]]=e[r[i]]);return n};class g0{constructor({url:t="",headers:n={},fetch:r}){this.url=t,this.headers=n,this.fetch=sh(r),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}async signOut(t,n=go[0]){if(go.indexOf(n)<0)throw new Error(`@supabase/auth-js: Parameter scope must be one of ${go.join(", ")}`);try{return await L(this.fetch,"POST",`${this.url}/logout?scope=${n}`,{headers:this.headers,jwt:t,noResolveJson:!0}),{data:null,error:null}}catch(r){if(R(r))return{data:null,error:r};throw r}}async inviteUserByEmail(t,n={}){try{return await L(this.fetch,"POST",`${this.url}/invite`,{body:{email:t,data:n.data},headers:this.headers,redirectTo:n.redirectTo,xform:gt})}catch(r){if(R(r))return{data:{user:null},error:r};throw r}}async generateLink(t){try{const{options:n}=t,r=v0(t,["options"]),i=Object.assign(Object.assign({},r),n);return"newEmail"in r&&(i.new_email=r==null?void 0:r.newEmail,delete i.newEmail),await L(this.fetch,"POST",`${this.url}/admin/generate_link`,{body:i,headers:this.headers,xform:f0,redirectTo:n==null?void 0:n.redirectTo})}catch(n){if(R(n))return{data:{properties:null,user:null},error:n};throw n}}async createUser(t){try{return await L(this.fetch,"POST",`${this.url}/admin/users`,{body:t,headers:this.headers,xform:gt})}catch(n){if(R(n))return{data:{user:null},error:n};throw n}}async listUsers(t){var n,r,i,s,o,l,a;try{const u={nextPage:null,lastPage:0,total:0},c=await L(this.fetch,"GET",`${this.url}/admin/users`,{headers:this.headers,noResolveJson:!0,query:{page:(r=(n=t==null?void 0:t.page)===null||n===void 0?void 0:n.toString())!==null&&r!==void 0?r:"",per_page:(s=(i=t==null?void 0:t.perPage)===null||i===void 0?void 0:i.toString())!==null&&s!==void 0?s:""},xform:h0});if(c.error)throw c.error;const d=await c.json(),f=(o=c.headers.get("x-total-count"))!==null&&o!==void 0?o:0,g=(a=(l=c.headers.get("link"))===null||l===void 0?void 0:l.split(","))!==null&&a!==void 0?a:[];return g.length>0&&(g.forEach(m=>{const w=parseInt(m.split(";")[0].split("=")[1].substring(0,1)),_=JSON.parse(m.split(";")[1].split("=")[1]);u[`${_}Page`]=w}),u.total=parseInt(f)),{data:Object.assign(Object.assign({},d),u),error:null}}catch(u){if(R(u))return{data:{users:[]},error:u};throw u}}async getUserById(t){ln(t);try{return await L(this.fetch,"GET",`${this.url}/admin/users/${t}`,{headers:this.headers,xform:gt})}catch(n){if(R(n))return{data:{user:null},error:n};throw n}}async updateUserById(t,n){ln(t);try{return await L(this.fetch,"PUT",`${this.url}/admin/users/${t}`,{body:n,headers:this.headers,xform:gt})}catch(r){if(R(r))return{data:{user:null},error:r};throw r}}async deleteUser(t,n=!1){ln(t);try{return await L(this.fetch,"DELETE",`${this.url}/admin/users/${t}`,{headers:this.headers,body:{should_soft_delete:n},xform:gt})}catch(r){if(R(r))return{data:{user:null},error:r};throw r}}async _listFactors(t){ln(t.userId);try{const{data:n,error:r}=await L(this.fetch,"GET",`${this.url}/admin/users/${t.userId}/factors`,{headers:this.headers,xform:i=>({data:{factors:i},error:null})});return{data:n,error:r}}catch(n){if(R(n))return{data:null,error:n};throw n}}async _deleteFactor(t){ln(t.userId),ln(t.id);try{return{data:await L(this.fetch,"DELETE",`${this.url}/admin/users/${t.userId}/factors/${t.id}`,{headers:this.headers}),error:null}}catch(n){if(R(n))return{data:null,error:n};throw n}}}const m0={getItem:e=>Sr()?globalThis.localStorage.getItem(e):null,setItem:(e,t)=>{Sr()&&globalThis.localStorage.setItem(e,t)},removeItem:e=>{Sr()&&globalThis.localStorage.removeItem(e)}};function lc(e={}){return{getItem:t=>e[t]||null,setItem:(t,n)=>{e[t]=n},removeItem:t=>{delete e[t]}}}function y0(){if(typeof globalThis!="object")try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch{typeof self<"u"&&(self.globalThis=self)}}const an={debug:!!(globalThis&&Sr()&&globalThis.localStorage&&globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug")==="true")};class lh extends Error{constructor(t){super(t),this.isAcquireTimeout=!0}}class w0 extends lh{}async function _0(e,t,n){an.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquire lock",e,t);const r=new globalThis.AbortController;return t>0&&setTimeout(()=>{r.abort(),an.debug&&console.log("@supabase/gotrue-js: navigatorLock acquire timed out",e)},t),await Promise.resolve().then(()=>globalThis.navigator.locks.request(e,t===0?{mode:"exclusive",ifAvailable:!0}:{mode:"exclusive",signal:r.signal},async i=>{if(i){an.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquired",e,i.name);try{return await n()}finally{an.debug&&console.log("@supabase/gotrue-js: navigatorLock: released",e,i.name)}}else{if(t===0)throw an.debug&&console.log("@supabase/gotrue-js: navigatorLock: not immediately available",e),new w0(`Acquiring an exclusive Navigator LockManager lock "${e}" immediately failed`);if(an.debug)try{const s=await globalThis.navigator.locks.query();console.log("@supabase/gotrue-js: Navigator LockManager state",JSON.stringify(s,null,"  "))}catch(s){console.warn("@supabase/gotrue-js: Error when querying Navigator LockManager state",s)}return console.warn("@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request"),await n()}}))}y0();const S0={url:Ry,storageKey:Iy,autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:$y,flowType:"implicit",debug:!1,hasCustomAuthorizationHeader:!1};async function ac(e,t,n){return await n()}class Fr{constructor(t){var n,r;this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.hasCustomAuthorizationHeader=!1,this.suppressGetSessionWarning=!1,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=Fr.nextInstanceID,Fr.nextInstanceID+=1,this.instanceID>0&&De()&&console.warn("Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.");const i=Object.assign(Object.assign({},S0),t);if(this.logDebugMessages=!!i.debug,typeof i.debug=="function"&&(this.logger=i.debug),this.persistSession=i.persistSession,this.storageKey=i.storageKey,this.autoRefreshToken=i.autoRefreshToken,this.admin=new g0({url:i.url,headers:i.headers,fetch:i.fetch}),this.url=i.url,this.headers=i.headers,this.fetch=sh(i.fetch),this.lock=i.lock||ac,this.detectSessionInUrl=i.detectSessionInUrl,this.flowType=i.flowType,this.hasCustomAuthorizationHeader=i.hasCustomAuthorizationHeader,i.lock?this.lock=i.lock:De()&&(!((n=globalThis==null?void 0:globalThis.navigator)===null||n===void 0)&&n.locks)?this.lock=_0:this.lock=ac,this.jwks={keys:[]},this.jwks_cached_at=Number.MIN_SAFE_INTEGER,this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?i.storage?this.storage=i.storage:Sr()?this.storage=m0:(this.memoryStorage={},this.storage=lc(this.memoryStorage)):(this.memoryStorage={},this.storage=lc(this.memoryStorage)),De()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(s){console.error("Failed to create a new BroadcastChannel, multi-tab state changes will not be available",s)}(r=this.broadcastChannel)===null||r===void 0||r.addEventListener("message",async s=>{this._debug("received broadcast notification from other tab or client",s),await this._notifyAllSubscribers(s.data.event,s.data.session,!1)})}this.initialize()}_debug(...t){return this.logDebugMessages&&this.logger(`GoTrueClient@${this.instanceID} (${th}) ${new Date().toISOString()}`,...t),this}async initialize(){return this.initializePromise?await this.initializePromise:(this.initializePromise=(async()=>await this._acquireLock(-1,async()=>await this._initialize()))(),await this.initializePromise)}async _initialize(){var t;try{const n=Jy(window.location.href);let r="none";if(this._isImplicitGrantCallback(n)?r="implicit":await this._isPKCECallback(n)&&(r="pkce"),De()&&this.detectSessionInUrl&&r!=="none"){const{data:i,error:s}=await this._getSessionFromURL(n,r);if(s){if(this._debug("#_initialize()","error detecting session from URL",s),Dy(s)){const a=(t=s.details)===null||t===void 0?void 0:t.code;if(a==="identity_already_exists"||a==="identity_not_found"||a==="single_identity_not_deletable")return{error:s}}return await this._removeSession(),{error:s}}const{session:o,redirectType:l}=i;return this._debug("#_initialize()","detected session in URL",o,"redirect type",l),await this._saveSession(o),setTimeout(async()=>{l==="recovery"?await this._notifyAllSubscribers("PASSWORD_RECOVERY",o):await this._notifyAllSubscribers("SIGNED_IN",o)},0),{error:null}}return await this._recoverAndRefresh(),{error:null}}catch(n){return R(n)?{error:n}:{error:new rh("Unexpected error during initialization",n)}}finally{await this._handleVisibilityChange(),this._debug("#_initialize()","end")}}async signInAnonymously(t){var n,r,i;try{const s=await L(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{data:(r=(n=t==null?void 0:t.options)===null||n===void 0?void 0:n.data)!==null&&r!==void 0?r:{},gotrue_meta_security:{captcha_token:(i=t==null?void 0:t.options)===null||i===void 0?void 0:i.captchaToken}},xform:Ye}),{data:o,error:l}=s;if(l||!o)return{data:{user:null,session:null},error:l};const a=o.session,u=o.user;return o.session&&(await this._saveSession(o.session),await this._notifyAllSubscribers("SIGNED_IN",a)),{data:{user:u,session:a},error:null}}catch(s){if(R(s))return{data:{user:null,session:null},error:s};throw s}}async signUp(t){var n,r,i;try{let s;if("email"in t){const{email:c,password:d,options:f}=t;let g=null,m=null;this.flowType==="pkce"&&([g,m]=await on(this.storage,this.storageKey)),s=await L(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,redirectTo:f==null?void 0:f.emailRedirectTo,body:{email:c,password:d,data:(n=f==null?void 0:f.data)!==null&&n!==void 0?n:{},gotrue_meta_security:{captcha_token:f==null?void 0:f.captchaToken},code_challenge:g,code_challenge_method:m},xform:Ye})}else if("phone"in t){const{phone:c,password:d,options:f}=t;s=await L(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{phone:c,password:d,data:(r=f==null?void 0:f.data)!==null&&r!==void 0?r:{},channel:(i=f==null?void 0:f.channel)!==null&&i!==void 0?i:"sms",gotrue_meta_security:{captcha_token:f==null?void 0:f.captchaToken}},xform:Ye})}else throw new gi("You must provide either an email or phone number and a password");const{data:o,error:l}=s;if(l||!o)return{data:{user:null,session:null},error:l};const a=o.session,u=o.user;return o.session&&(await this._saveSession(o.session),await this._notifyAllSubscribers("SIGNED_IN",a)),{data:{user:u,session:a},error:null}}catch(s){if(R(s))return{data:{user:null,session:null},error:s};throw s}}async signInWithPassword(t){try{let n;if("email"in t){const{email:s,password:o,options:l}=t;n=await L(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{email:s,password:o,gotrue_meta_security:{captcha_token:l==null?void 0:l.captchaToken}},xform:oc})}else if("phone"in t){const{phone:s,password:o,options:l}=t;n=await L(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{phone:s,password:o,gotrue_meta_security:{captcha_token:l==null?void 0:l.captchaToken}},xform:oc})}else throw new gi("You must provide either an email or phone number and a password");const{data:r,error:i}=n;return i?{data:{user:null,session:null},error:i}:!r||!r.session||!r.user?{data:{user:null,session:null},error:new vi}:(r.session&&(await this._saveSession(r.session),await this._notifyAllSubscribers("SIGNED_IN",r.session)),{data:Object.assign({user:r.user,session:r.session},r.weak_password?{weakPassword:r.weak_password}:null),error:i})}catch(n){if(R(n))return{data:{user:null,session:null},error:n};throw n}}async signInWithOAuth(t){var n,r,i,s;return await this._handleProviderSignIn(t.provider,{redirectTo:(n=t.options)===null||n===void 0?void 0:n.redirectTo,scopes:(r=t.options)===null||r===void 0?void 0:r.scopes,queryParams:(i=t.options)===null||i===void 0?void 0:i.queryParams,skipBrowserRedirect:(s=t.options)===null||s===void 0?void 0:s.skipBrowserRedirect})}async exchangeCodeForSession(t){return await this.initializePromise,this._acquireLock(-1,async()=>this._exchangeCodeForSession(t))}async signInWithWeb3(t){const{chain:n}=t;if(n==="solana")return await this.signInWithSolana(t);throw new Error(`@supabase/auth-js: Unsupported chain "${n}"`)}async signInWithSolana(t){var n,r,i,s,o,l,a,u,c,d,f,g;let m,w;if("message"in t)m=t.message,w=t.signature;else{const{chain:_,wallet:v,statement:h,options:p}=t;let y;if(De())if(typeof v=="object")y=v;else{const E=window;if("solana"in E&&typeof E.solana=="object"&&("signIn"in E.solana&&typeof E.solana.signIn=="function"||"signMessage"in E.solana&&typeof E.solana.signMessage=="function"))y=E.solana;else throw new Error("@supabase/auth-js: No compatible Solana wallet interface on the window object (window.solana) detected. Make sure the user already has a wallet installed and connected for this app. Prefer passing the wallet interface object directly to signInWithWeb3({ chain: 'solana', wallet: resolvedUserWallet }) instead.")}else{if(typeof v!="object"||!(p!=null&&p.url))throw new Error("@supabase/auth-js: Both wallet and url must be specified in non-browser environments.");y=v}const k=new URL((n=p==null?void 0:p.url)!==null&&n!==void 0?n:window.location.href);if("signIn"in y&&y.signIn){const E=await y.signIn(Object.assign(Object.assign(Object.assign({issuedAt:new Date().toISOString()},p==null?void 0:p.signInWithSolana),{version:"1",domain:k.host,uri:k.href}),h?{statement:h}:null));let C;if(Array.isArray(E)&&E[0]&&typeof E[0]=="object")C=E[0];else if(E&&typeof E=="object"&&"signedMessage"in E&&"signature"in E)C=E;else throw new Error("@supabase/auth-js: Wallet method signIn() returned unrecognized value");if("signedMessage"in C&&"signature"in C&&(typeof C.signedMessage=="string"||C.signedMessage instanceof Uint8Array)&&C.signature instanceof Uint8Array)m=typeof C.signedMessage=="string"?C.signedMessage:new TextDecoder().decode(C.signedMessage),w=C.signature;else throw new Error("@supabase/auth-js: Wallet method signIn() API returned object without signedMessage and signature fields")}else{if(!("signMessage"in y)||typeof y.signMessage!="function"||!("publicKey"in y)||typeof y!="object"||!y.publicKey||!("toBase58"in y.publicKey)||typeof y.publicKey.toBase58!="function")throw new Error("@supabase/auth-js: Wallet does not have a compatible signMessage() and publicKey.toBase58() API");m=[`${k.host} wants you to sign in with your Solana account:`,y.publicKey.toBase58(),...h?["",h,""]:[""],"Version: 1",`URI: ${k.href}`,`Issued At: ${(i=(r=p==null?void 0:p.signInWithSolana)===null||r===void 0?void 0:r.issuedAt)!==null&&i!==void 0?i:new Date().toISOString()}`,...!((s=p==null?void 0:p.signInWithSolana)===null||s===void 0)&&s.notBefore?[`Not Before: ${p.signInWithSolana.notBefore}`]:[],...!((o=p==null?void 0:p.signInWithSolana)===null||o===void 0)&&o.expirationTime?[`Expiration Time: ${p.signInWithSolana.expirationTime}`]:[],...!((l=p==null?void 0:p.signInWithSolana)===null||l===void 0)&&l.chainId?[`Chain ID: ${p.signInWithSolana.chainId}`]:[],...!((a=p==null?void 0:p.signInWithSolana)===null||a===void 0)&&a.nonce?[`Nonce: ${p.signInWithSolana.nonce}`]:[],...!((u=p==null?void 0:p.signInWithSolana)===null||u===void 0)&&u.requestId?[`Request ID: ${p.signInWithSolana.requestId}`]:[],...!((d=(c=p==null?void 0:p.signInWithSolana)===null||c===void 0?void 0:c.resources)===null||d===void 0)&&d.length?["Resources",...p.signInWithSolana.resources.map(C=>`- ${C}`)]:[]].join(`
`);const E=await y.signMessage(new TextEncoder().encode(m),"utf8");if(!E||!(E instanceof Uint8Array))throw new Error("@supabase/auth-js: Wallet signMessage() API returned an recognized value");w=E}}try{const{data:_,error:v}=await L(this.fetch,"POST",`${this.url}/token?grant_type=web3`,{headers:this.headers,body:Object.assign({chain:"solana",message:m,signature:Hy(w)},!((f=t.options)===null||f===void 0)&&f.captchaToken?{gotrue_meta_security:{captcha_token:(g=t.options)===null||g===void 0?void 0:g.captchaToken}}:null),xform:Ye});if(v)throw v;return!_||!_.session||!_.user?{data:{user:null,session:null},error:new vi}:(_.session&&(await this._saveSession(_.session),await this._notifyAllSubscribers("SIGNED_IN",_.session)),{data:Object.assign({},_),error:v})}catch(_){if(R(_))return{data:{user:null,session:null},error:_};throw _}}async _exchangeCodeForSession(t){const n=await yi(this.storage,`${this.storageKey}-code-verifier`),[r,i]=(n??"").split("/");try{const{data:s,error:o}=await L(this.fetch,"POST",`${this.url}/token?grant_type=pkce`,{headers:this.headers,body:{auth_code:t,code_verifier:r},xform:Ye});if(await wi(this.storage,`${this.storageKey}-code-verifier`),o)throw o;return!s||!s.session||!s.user?{data:{user:null,session:null,redirectType:null},error:new vi}:(s.session&&(await this._saveSession(s.session),await this._notifyAllSubscribers("SIGNED_IN",s.session)),{data:Object.assign(Object.assign({},s),{redirectType:i??null}),error:o})}catch(s){if(R(s))return{data:{user:null,session:null,redirectType:null},error:s};throw s}}async signInWithIdToken(t){try{const{options:n,provider:r,token:i,access_token:s,nonce:o}=t,l=await L(this.fetch,"POST",`${this.url}/token?grant_type=id_token`,{headers:this.headers,body:{provider:r,id_token:i,access_token:s,nonce:o,gotrue_meta_security:{captcha_token:n==null?void 0:n.captchaToken}},xform:Ye}),{data:a,error:u}=l;return u?{data:{user:null,session:null},error:u}:!a||!a.session||!a.user?{data:{user:null,session:null},error:new vi}:(a.session&&(await this._saveSession(a.session),await this._notifyAllSubscribers("SIGNED_IN",a.session)),{data:a,error:u})}catch(n){if(R(n))return{data:{user:null,session:null},error:n};throw n}}async signInWithOtp(t){var n,r,i,s,o;try{if("email"in t){const{email:l,options:a}=t;let u=null,c=null;this.flowType==="pkce"&&([u,c]=await on(this.storage,this.storageKey));const{error:d}=await L(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{email:l,data:(n=a==null?void 0:a.data)!==null&&n!==void 0?n:{},create_user:(r=a==null?void 0:a.shouldCreateUser)!==null&&r!==void 0?r:!0,gotrue_meta_security:{captcha_token:a==null?void 0:a.captchaToken},code_challenge:u,code_challenge_method:c},redirectTo:a==null?void 0:a.emailRedirectTo});return{data:{user:null,session:null},error:d}}if("phone"in t){const{phone:l,options:a}=t,{data:u,error:c}=await L(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{phone:l,data:(i=a==null?void 0:a.data)!==null&&i!==void 0?i:{},create_user:(s=a==null?void 0:a.shouldCreateUser)!==null&&s!==void 0?s:!0,gotrue_meta_security:{captcha_token:a==null?void 0:a.captchaToken},channel:(o=a==null?void 0:a.channel)!==null&&o!==void 0?o:"sms"}});return{data:{user:null,session:null,messageId:u==null?void 0:u.message_id},error:c}}throw new gi("You must provide either an email or phone number.")}catch(l){if(R(l))return{data:{user:null,session:null},error:l};throw l}}async verifyOtp(t){var n,r;try{let i,s;"options"in t&&(i=(n=t.options)===null||n===void 0?void 0:n.redirectTo,s=(r=t.options)===null||r===void 0?void 0:r.captchaToken);const{data:o,error:l}=await L(this.fetch,"POST",`${this.url}/verify`,{headers:this.headers,body:Object.assign(Object.assign({},t),{gotrue_meta_security:{captcha_token:s}}),redirectTo:i,xform:Ye});if(l)throw l;if(!o)throw new Error("An error occurred on token verification.");const a=o.session,u=o.user;return a!=null&&a.access_token&&(await this._saveSession(a),await this._notifyAllSubscribers(t.type=="recovery"?"PASSWORD_RECOVERY":"SIGNED_IN",a)),{data:{user:u,session:a},error:null}}catch(i){if(R(i))return{data:{user:null,session:null},error:i};throw i}}async signInWithSSO(t){var n,r,i;try{let s=null,o=null;return this.flowType==="pkce"&&([s,o]=await on(this.storage,this.storageKey)),await L(this.fetch,"POST",`${this.url}/sso`,{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in t?{provider_id:t.providerId}:null),"domain"in t?{domain:t.domain}:null),{redirect_to:(r=(n=t.options)===null||n===void 0?void 0:n.redirectTo)!==null&&r!==void 0?r:void 0}),!((i=t==null?void 0:t.options)===null||i===void 0)&&i.captchaToken?{gotrue_meta_security:{captcha_token:t.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:s,code_challenge_method:o}),headers:this.headers,xform:d0})}catch(s){if(R(s))return{data:null,error:s};throw s}}async reauthenticate(){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._reauthenticate())}async _reauthenticate(){try{return await this._useSession(async t=>{const{data:{session:n},error:r}=t;if(r)throw r;if(!n)throw new ct;const{error:i}=await L(this.fetch,"GET",`${this.url}/reauthenticate`,{headers:this.headers,jwt:n.access_token});return{data:{user:null,session:null},error:i}})}catch(t){if(R(t))return{data:{user:null,session:null},error:t};throw t}}async resend(t){try{const n=`${this.url}/resend`;if("email"in t){const{email:r,type:i,options:s}=t,{error:o}=await L(this.fetch,"POST",n,{headers:this.headers,body:{email:r,type:i,gotrue_meta_security:{captcha_token:s==null?void 0:s.captchaToken}},redirectTo:s==null?void 0:s.emailRedirectTo});return{data:{user:null,session:null},error:o}}else if("phone"in t){const{phone:r,type:i,options:s}=t,{data:o,error:l}=await L(this.fetch,"POST",n,{headers:this.headers,body:{phone:r,type:i,gotrue_meta_security:{captcha_token:s==null?void 0:s.captchaToken}}});return{data:{user:null,session:null,messageId:o==null?void 0:o.message_id},error:l}}throw new gi("You must provide either an email or phone number and a type")}catch(n){if(R(n))return{data:{user:null,session:null},error:n};throw n}}async getSession(){return await this.initializePromise,await this._acquireLock(-1,async()=>this._useSession(async n=>n))}async _acquireLock(t,n){this._debug("#_acquireLock","begin",t);try{if(this.lockAcquired){const r=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),i=(async()=>(await r,await n()))();return this.pendingInLock.push((async()=>{try{await i}catch{}})()),i}return await this.lock(`lock:${this.storageKey}`,t,async()=>{this._debug("#_acquireLock","lock acquired for storage key",this.storageKey);try{this.lockAcquired=!0;const r=n();for(this.pendingInLock.push((async()=>{try{await r}catch{}})()),await r;this.pendingInLock.length;){const i=[...this.pendingInLock];await Promise.all(i),this.pendingInLock.splice(0,i.length)}return await r}finally{this._debug("#_acquireLock","lock released for storage key",this.storageKey),this.lockAcquired=!1}})}finally{this._debug("#_acquireLock","end")}}async _useSession(t){this._debug("#_useSession","begin");try{const n=await this.__loadSession();return await t(n)}finally{this._debug("#_useSession","end")}}async __loadSession(){this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",new Error().stack);try{let t=null;const n=await yi(this.storage,this.storageKey);if(this._debug("#getSession()","session from storage",n),n!==null&&(this._isValidSession(n)?t=n:(this._debug("#getSession()","session from storage is not valid"),await this._removeSession())),!t)return{data:{session:null},error:null};const r=t.expires_at?t.expires_at*1e3-Date.now()<ho:!1;if(this._debug("#__loadSession()",`session has${r?"":" not"} expired`,"expires_at",t.expires_at),!r){if(this.storage.isServer){let o=this.suppressGetSessionWarning;t=new Proxy(t,{get:(a,u,c)=>(!o&&u==="user"&&(console.warn("Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server."),o=!0,this.suppressGetSessionWarning=!0),Reflect.get(a,u,c))})}return{data:{session:t},error:null}}const{session:i,error:s}=await this._callRefreshToken(t.refresh_token);return s?{data:{session:null},error:s}:{data:{session:i},error:null}}finally{this._debug("#__loadSession()","end")}}async getUser(t){return t?await this._getUser(t):(await this.initializePromise,await this._acquireLock(-1,async()=>await this._getUser()))}async _getUser(t){try{return t?await L(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:t,xform:gt}):await this._useSession(async n=>{var r,i,s;const{data:o,error:l}=n;if(l)throw l;return!(!((r=o.session)===null||r===void 0)&&r.access_token)&&!this.hasCustomAuthorizationHeader?{data:{user:null},error:new ct}:await L(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:(s=(i=o.session)===null||i===void 0?void 0:i.access_token)!==null&&s!==void 0?s:void 0,xform:gt})})}catch(n){if(R(n))return Uy(n)&&(await this._removeSession(),await wi(this.storage,`${this.storageKey}-code-verifier`)),{data:{user:null},error:n};throw n}}async updateUser(t,n={}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._updateUser(t,n))}async _updateUser(t,n={}){try{return await this._useSession(async r=>{const{data:i,error:s}=r;if(s)throw s;if(!i.session)throw new ct;const o=i.session;let l=null,a=null;this.flowType==="pkce"&&t.email!=null&&([l,a]=await on(this.storage,this.storageKey));const{data:u,error:c}=await L(this.fetch,"PUT",`${this.url}/user`,{headers:this.headers,redirectTo:n==null?void 0:n.emailRedirectTo,body:Object.assign(Object.assign({},t),{code_challenge:l,code_challenge_method:a}),jwt:o.access_token,xform:gt});if(c)throw c;return o.user=u.user,await this._saveSession(o),await this._notifyAllSubscribers("USER_UPDATED",o),{data:{user:o.user},error:null}})}catch(r){if(R(r))return{data:{user:null},error:r};throw r}}async setSession(t){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._setSession(t))}async _setSession(t){try{if(!t.access_token||!t.refresh_token)throw new ct;const n=Date.now()/1e3;let r=n,i=!0,s=null;const{payload:o}=vo(t.access_token);if(o.exp&&(r=o.exp,i=r<=n),i){const{session:l,error:a}=await this._callRefreshToken(t.refresh_token);if(a)return{data:{user:null,session:null},error:a};if(!l)return{data:{user:null,session:null},error:null};s=l}else{const{data:l,error:a}=await this._getUser(t.access_token);if(a)throw a;s={access_token:t.access_token,refresh_token:t.refresh_token,user:l.user,token_type:"bearer",expires_in:r-n,expires_at:r},await this._saveSession(s),await this._notifyAllSubscribers("SIGNED_IN",s)}return{data:{user:s.user,session:s},error:null}}catch(n){if(R(n))return{data:{session:null,user:null},error:n};throw n}}async refreshSession(t){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._refreshSession(t))}async _refreshSession(t){try{return await this._useSession(async n=>{var r;if(!t){const{data:o,error:l}=n;if(l)throw l;t=(r=o.session)!==null&&r!==void 0?r:void 0}if(!(t!=null&&t.refresh_token))throw new ct;const{session:i,error:s}=await this._callRefreshToken(t.refresh_token);return s?{data:{user:null,session:null},error:s}:i?{data:{user:i.user,session:i},error:null}:{data:{user:null,session:null},error:null}})}catch(n){if(R(n))return{data:{user:null,session:null},error:n};throw n}}async _getSessionFromURL(t,n){try{if(!De())throw new mi("No browser detected.");if(t.error||t.error_description||t.error_code)throw new mi(t.error_description||"Error in URL with unspecified error_description",{error:t.error||"unspecified_error",code:t.error_code||"unspecified_code"});switch(n){case"implicit":if(this.flowType==="pkce")throw new ec("Not a valid PKCE flow url.");break;case"pkce":if(this.flowType==="implicit")throw new mi("Not a valid implicit grant flow url.");break;default:}if(n==="pkce"){if(this._debug("#_initialize()","begin","is PKCE flow",!0),!t.code)throw new ec("No code detected.");const{data:h,error:p}=await this._exchangeCodeForSession(t.code);if(p)throw p;const y=new URL(window.location.href);return y.searchParams.delete("code"),window.history.replaceState(window.history.state,"",y.toString()),{data:{session:h.session,redirectType:null},error:null}}const{provider_token:r,provider_refresh_token:i,access_token:s,refresh_token:o,expires_in:l,expires_at:a,token_type:u}=t;if(!s||!l||!o||!u)throw new mi("No session defined in URL");const c=Math.round(Date.now()/1e3),d=parseInt(l);let f=c+d;a&&(f=parseInt(a));const g=f-c;g*1e3<=un&&console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${g}s, should have been closer to ${d}s`);const m=f-d;c-m>=120?console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale",m,f,c):c-m<0&&console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew",m,f,c);const{data:w,error:_}=await this._getUser(s);if(_)throw _;const v={provider_token:r,provider_refresh_token:i,access_token:s,expires_in:d,expires_at:f,refresh_token:o,token_type:u,user:w.user};return window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),{data:{session:v,redirectType:t.type},error:null}}catch(r){if(R(r))return{data:{session:null,redirectType:null},error:r};throw r}}_isImplicitGrantCallback(t){return!!(t.access_token||t.error_description)}async _isPKCECallback(t){const n=await yi(this.storage,`${this.storageKey}-code-verifier`);return!!(t.code&&n)}async signOut(t={scope:"global"}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._signOut(t))}async _signOut({scope:t}={scope:"global"}){return await this._useSession(async n=>{var r;const{data:i,error:s}=n;if(s)return{error:s};const o=(r=i.session)===null||r===void 0?void 0:r.access_token;if(o){const{error:l}=await this.admin.signOut(o,t);if(l&&!(by(l)&&(l.status===404||l.status===401||l.status===403)))return{error:l}}return t!=="others"&&(await this._removeSession(),await wi(this.storage,`${this.storageKey}-code-verifier`)),{error:null}})}onAuthStateChange(t){const n=Ky(),r={id:n,callback:t,unsubscribe:()=>{this._debug("#unsubscribe()","state change callback with id removed",n),this.stateChangeEmitters.delete(n)}};return this._debug("#onAuthStateChange()","registered callback with id",n),this.stateChangeEmitters.set(n,r),(async()=>(await this.initializePromise,await this._acquireLock(-1,async()=>{this._emitInitialSession(n)})))(),{data:{subscription:r}}}async _emitInitialSession(t){return await this._useSession(async n=>{var r,i;try{const{data:{session:s},error:o}=n;if(o)throw o;await((r=this.stateChangeEmitters.get(t))===null||r===void 0?void 0:r.callback("INITIAL_SESSION",s)),this._debug("INITIAL_SESSION","callback id",t,"session",s)}catch(s){await((i=this.stateChangeEmitters.get(t))===null||i===void 0?void 0:i.callback("INITIAL_SESSION",null)),this._debug("INITIAL_SESSION","callback id",t,"error",s),console.error(s)}})}async resetPasswordForEmail(t,n={}){let r=null,i=null;this.flowType==="pkce"&&([r,i]=await on(this.storage,this.storageKey,!0));try{return await L(this.fetch,"POST",`${this.url}/recover`,{body:{email:t,code_challenge:r,code_challenge_method:i,gotrue_meta_security:{captcha_token:n.captchaToken}},headers:this.headers,redirectTo:n.redirectTo})}catch(s){if(R(s))return{data:null,error:s};throw s}}async getUserIdentities(){var t;try{const{data:n,error:r}=await this.getUser();if(r)throw r;return{data:{identities:(t=n.user.identities)!==null&&t!==void 0?t:[]},error:null}}catch(n){if(R(n))return{data:null,error:n};throw n}}async linkIdentity(t){var n;try{const{data:r,error:i}=await this._useSession(async s=>{var o,l,a,u,c;const{data:d,error:f}=s;if(f)throw f;const g=await this._getUrlForProvider(`${this.url}/user/identities/authorize`,t.provider,{redirectTo:(o=t.options)===null||o===void 0?void 0:o.redirectTo,scopes:(l=t.options)===null||l===void 0?void 0:l.scopes,queryParams:(a=t.options)===null||a===void 0?void 0:a.queryParams,skipBrowserRedirect:!0});return await L(this.fetch,"GET",g,{headers:this.headers,jwt:(c=(u=d.session)===null||u===void 0?void 0:u.access_token)!==null&&c!==void 0?c:void 0})});if(i)throw i;return De()&&!(!((n=t.options)===null||n===void 0)&&n.skipBrowserRedirect)&&window.location.assign(r==null?void 0:r.url),{data:{provider:t.provider,url:r==null?void 0:r.url},error:null}}catch(r){if(R(r))return{data:{provider:t.provider,url:null},error:r};throw r}}async unlinkIdentity(t){try{return await this._useSession(async n=>{var r,i;const{data:s,error:o}=n;if(o)throw o;return await L(this.fetch,"DELETE",`${this.url}/user/identities/${t.identity_id}`,{headers:this.headers,jwt:(i=(r=s.session)===null||r===void 0?void 0:r.access_token)!==null&&i!==void 0?i:void 0})})}catch(n){if(R(n))return{data:null,error:n};throw n}}async _refreshAccessToken(t){const n=`#_refreshAccessToken(${t.substring(0,5)}...)`;this._debug(n,"begin");try{const r=Date.now();return await Yy(async i=>(i>0&&await Gy(200*Math.pow(2,i-1)),this._debug(n,"refreshing attempt",i),await L(this.fetch,"POST",`${this.url}/token?grant_type=refresh_token`,{body:{refresh_token:t},headers:this.headers,xform:Ye})),(i,s)=>{const o=200*Math.pow(2,i);return s&&po(s)&&Date.now()+o-r<un})}catch(r){if(this._debug(n,"error",r),R(r))return{data:{session:null,user:null},error:r};throw r}finally{this._debug(n,"end")}}_isValidSession(t){return typeof t=="object"&&t!==null&&"access_token"in t&&"refresh_token"in t&&"expires_at"in t}async _handleProviderSignIn(t,n){const r=await this._getUrlForProvider(`${this.url}/authorize`,t,{redirectTo:n.redirectTo,scopes:n.scopes,queryParams:n.queryParams});return this._debug("#_handleProviderSignIn()","provider",t,"options",n,"url",r),De()&&!n.skipBrowserRedirect&&window.location.assign(r),{data:{provider:t,url:r},error:null}}async _recoverAndRefresh(){var t;const n="#_recoverAndRefresh()";this._debug(n,"begin");try{const r=await yi(this.storage,this.storageKey);if(this._debug(n,"session from storage",r),!this._isValidSession(r)){this._debug(n,"session is not valid"),r!==null&&await this._removeSession();return}const i=((t=r.expires_at)!==null&&t!==void 0?t:1/0)*1e3-Date.now()<ho;if(this._debug(n,`session has${i?"":" not"} expired with margin of ${ho}s`),i){if(this.autoRefreshToken&&r.refresh_token){const{error:s}=await this._callRefreshToken(r.refresh_token);s&&(console.error(s),po(s)||(this._debug(n,"refresh failed with a non-retryable error, removing the session",s),await this._removeSession()))}}else await this._notifyAllSubscribers("SIGNED_IN",r)}catch(r){this._debug(n,"error",r),console.error(r);return}finally{this._debug(n,"end")}}async _callRefreshToken(t){var n,r;if(!t)throw new ct;if(this.refreshingDeferred)return this.refreshingDeferred.promise;const i=`#_callRefreshToken(${t.substring(0,5)}...)`;this._debug(i,"begin");try{this.refreshingDeferred=new $s;const{data:s,error:o}=await this._refreshAccessToken(t);if(o)throw o;if(!s.session)throw new ct;await this._saveSession(s.session),await this._notifyAllSubscribers("TOKEN_REFRESHED",s.session);const l={session:s.session,error:null};return this.refreshingDeferred.resolve(l),l}catch(s){if(this._debug(i,"error",s),R(s)){const o={session:null,error:s};return po(s)||await this._removeSession(),(n=this.refreshingDeferred)===null||n===void 0||n.resolve(o),o}throw(r=this.refreshingDeferred)===null||r===void 0||r.reject(s),s}finally{this.refreshingDeferred=null,this._debug(i,"end")}}async _notifyAllSubscribers(t,n,r=!0){const i=`#_notifyAllSubscribers(${t})`;this._debug(i,"begin",n,`broadcast = ${r}`);try{this.broadcastChannel&&r&&this.broadcastChannel.postMessage({event:t,session:n});const s=[],o=Array.from(this.stateChangeEmitters.values()).map(async l=>{try{await l.callback(t,n)}catch(a){s.push(a)}});if(await Promise.all(o),s.length>0){for(let l=0;l<s.length;l+=1)console.error(s[l]);throw s[0]}}finally{this._debug(i,"end")}}async _saveSession(t){this._debug("#_saveSession()",t),this.suppressGetSessionWarning=!0,await oh(this.storage,this.storageKey,t)}async _removeSession(){this._debug("#_removeSession()"),await wi(this.storage,this.storageKey),await this._notifyAllSubscribers("SIGNED_OUT",null)}_removeVisibilityChangedCallback(){this._debug("#_removeVisibilityChangedCallback()");const t=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{t&&De()&&(window!=null&&window.removeEventListener)&&window.removeEventListener("visibilitychange",t)}catch(n){console.error("removing visibilitychange callback failed",n)}}async _startAutoRefresh(){await this._stopAutoRefresh(),this._debug("#_startAutoRefresh()");const t=setInterval(()=>this._autoRefreshTokenTick(),un);this.autoRefreshTicker=t,t&&typeof t=="object"&&typeof t.unref=="function"?t.unref():typeof Deno<"u"&&typeof Deno.unrefTimer=="function"&&Deno.unrefTimer(t),setTimeout(async()=>{await this.initializePromise,await this._autoRefreshTokenTick()},0)}async _stopAutoRefresh(){this._debug("#_stopAutoRefresh()");const t=this.autoRefreshTicker;this.autoRefreshTicker=null,t&&clearInterval(t)}async startAutoRefresh(){this._removeVisibilityChangedCallback(),await this._startAutoRefresh()}async stopAutoRefresh(){this._removeVisibilityChangedCallback(),await this._stopAutoRefresh()}async _autoRefreshTokenTick(){this._debug("#_autoRefreshTokenTick()","begin");try{await this._acquireLock(0,async()=>{try{const t=Date.now();try{return await this._useSession(async n=>{const{data:{session:r}}=n;if(!r||!r.refresh_token||!r.expires_at){this._debug("#_autoRefreshTokenTick()","no session");return}const i=Math.floor((r.expires_at*1e3-t)/un);this._debug("#_autoRefreshTokenTick()",`access token expires in ${i} ticks, a tick lasts ${un}ms, refresh threshold is ${yl} ticks`),i<=yl&&await this._callRefreshToken(r.refresh_token)})}catch(n){console.error("Auto refresh tick failed with error. This is likely a transient error.",n)}}finally{this._debug("#_autoRefreshTokenTick()","end")}})}catch(t){if(t.isAcquireTimeout||t instanceof lh)this._debug("auto refresh token tick lock not available");else throw t}}async _handleVisibilityChange(){if(this._debug("#_handleVisibilityChange()"),!De()||!(window!=null&&window.addEventListener))return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=async()=>await this._onVisibilityChanged(!1),window==null||window.addEventListener("visibilitychange",this.visibilityChangedCallback),await this._onVisibilityChanged(!0)}catch(t){console.error("_handleVisibilityChange",t)}}async _onVisibilityChanged(t){const n=`#_onVisibilityChanged(${t})`;this._debug(n,"visibilityState",document.visibilityState),document.visibilityState==="visible"?(this.autoRefreshToken&&this._startAutoRefresh(),t||(await this.initializePromise,await this._acquireLock(-1,async()=>{if(document.visibilityState!=="visible"){this._debug(n,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting");return}await this._recoverAndRefresh()}))):document.visibilityState==="hidden"&&this.autoRefreshToken&&this._stopAutoRefresh()}async _getUrlForProvider(t,n,r){const i=[`provider=${encodeURIComponent(n)}`];if(r!=null&&r.redirectTo&&i.push(`redirect_to=${encodeURIComponent(r.redirectTo)}`),r!=null&&r.scopes&&i.push(`scopes=${encodeURIComponent(r.scopes)}`),this.flowType==="pkce"){const[s,o]=await on(this.storage,this.storageKey),l=new URLSearchParams({code_challenge:`${encodeURIComponent(s)}`,code_challenge_method:`${encodeURIComponent(o)}`});i.push(l.toString())}if(r!=null&&r.queryParams){const s=new URLSearchParams(r.queryParams);i.push(s.toString())}return r!=null&&r.skipBrowserRedirect&&i.push(`skip_http_redirect=${r.skipBrowserRedirect}`),`${t}?${i.join("&")}`}async _unenroll(t){try{return await this._useSession(async n=>{var r;const{data:i,error:s}=n;return s?{data:null,error:s}:await L(this.fetch,"DELETE",`${this.url}/factors/${t.factorId}`,{headers:this.headers,jwt:(r=i==null?void 0:i.session)===null||r===void 0?void 0:r.access_token})})}catch(n){if(R(n))return{data:null,error:n};throw n}}async _enroll(t){try{return await this._useSession(async n=>{var r,i;const{data:s,error:o}=n;if(o)return{data:null,error:o};const l=Object.assign({friendly_name:t.friendlyName,factor_type:t.factorType},t.factorType==="phone"?{phone:t.phone}:{issuer:t.issuer}),{data:a,error:u}=await L(this.fetch,"POST",`${this.url}/factors`,{body:l,headers:this.headers,jwt:(r=s==null?void 0:s.session)===null||r===void 0?void 0:r.access_token});return u?{data:null,error:u}:(t.factorType==="totp"&&(!((i=a==null?void 0:a.totp)===null||i===void 0)&&i.qr_code)&&(a.totp.qr_code=`data:image/svg+xml;utf-8,${a.totp.qr_code}`),{data:a,error:null})})}catch(n){if(R(n))return{data:null,error:n};throw n}}async _verify(t){return this._acquireLock(-1,async()=>{try{return await this._useSession(async n=>{var r;const{data:i,error:s}=n;if(s)return{data:null,error:s};const{data:o,error:l}=await L(this.fetch,"POST",`${this.url}/factors/${t.factorId}/verify`,{body:{code:t.code,challenge_id:t.challengeId},headers:this.headers,jwt:(r=i==null?void 0:i.session)===null||r===void 0?void 0:r.access_token});return l?{data:null,error:l}:(await this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+o.expires_in},o)),await this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",o),{data:o,error:l})})}catch(n){if(R(n))return{data:null,error:n};throw n}})}async _challenge(t){return this._acquireLock(-1,async()=>{try{return await this._useSession(async n=>{var r;const{data:i,error:s}=n;return s?{data:null,error:s}:await L(this.fetch,"POST",`${this.url}/factors/${t.factorId}/challenge`,{body:{channel:t.channel},headers:this.headers,jwt:(r=i==null?void 0:i.session)===null||r===void 0?void 0:r.access_token})})}catch(n){if(R(n))return{data:null,error:n};throw n}})}async _challengeAndVerify(t){const{data:n,error:r}=await this._challenge({factorId:t.factorId});return r?{data:null,error:r}:await this._verify({factorId:t.factorId,challengeId:n.id,code:t.code})}async _listFactors(){const{data:{user:t},error:n}=await this.getUser();if(n)return{data:null,error:n};const r=(t==null?void 0:t.factors)||[],i=r.filter(o=>o.factor_type==="totp"&&o.status==="verified"),s=r.filter(o=>o.factor_type==="phone"&&o.status==="verified");return{data:{all:r,totp:i,phone:s},error:null}}async _getAuthenticatorAssuranceLevel(){return this._acquireLock(-1,async()=>await this._useSession(async t=>{var n,r;const{data:{session:i},error:s}=t;if(s)return{data:null,error:s};if(!i)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};const{payload:o}=vo(i.access_token);let l=null;o.aal&&(l=o.aal);let a=l;((r=(n=i.user.factors)===null||n===void 0?void 0:n.filter(d=>d.status==="verified"))!==null&&r!==void 0?r:[]).length>0&&(a="aal2");const c=o.amr||[];return{data:{currentLevel:l,nextLevel:a,currentAuthenticationMethods:c},error:null}}))}async fetchJwk(t,n={keys:[]}){let r=n.keys.find(o=>o.kid===t);if(r||(r=this.jwks.keys.find(o=>o.kid===t),r&&this.jwks_cached_at+Ay>Date.now()))return r;const{data:i,error:s}=await L(this.fetch,"GET",`${this.url}/.well-known/jwks.json`,{headers:this.headers});if(s)throw s;if(!i.keys||i.keys.length===0)throw new _r("JWKS is empty");if(this.jwks=i,this.jwks_cached_at=Date.now(),r=i.keys.find(o=>o.kid===t),!r)throw new _r("No matching signing key found in JWKS");return r}async getClaims(t,n={keys:[]}){try{let r=t;if(!r){const{data:g,error:m}=await this.getSession();if(m||!g.session)return{data:null,error:m};r=g.session.access_token}const{header:i,payload:s,signature:o,raw:{header:l,payload:a}}=vo(r);if(i0(s.exp),!i.kid||i.alg==="HS256"||!("crypto"in globalThis&&"subtle"in globalThis.crypto)){const{error:g}=await this.getUser(r);if(g)throw g;return{data:{claims:s,header:i,signature:o},error:null}}const u=s0(i.alg),c=await this.fetchJwk(i.kid,n),d=await crypto.subtle.importKey("jwk",c,u,!0,["verify"]);if(!await crypto.subtle.verify(u,d,o,Vy(`${l}.${a}`)))throw new _r("Invalid JWT signature");return{data:{claims:s,header:i,signature:o},error:null}}catch(r){if(R(r))return{data:null,error:r};throw r}}}Fr.nextInstanceID=0;const k0=Fr;class E0 extends k0{constructor(t){super(t)}}var x0=globalThis&&globalThis.__awaiter||function(e,t,n,r){function i(s){return s instanceof n?s:new n(function(o){o(s)})}return new(n||(n=Promise))(function(s,o){function l(c){try{u(r.next(c))}catch(d){o(d)}}function a(c){try{u(r.throw(c))}catch(d){o(d)}}function u(c){c.done?s(c.value):i(c.value).then(l,a)}u((r=r.apply(e,t||[])).next())})};class C0{constructor(t,n,r){var i,s,o;if(this.supabaseUrl=t,this.supabaseKey=n,!t)throw new Error("supabaseUrl is required.");if(!n)throw new Error("supabaseKey is required.");const l=jy(t),a=new URL(l);this.realtimeUrl=new URL("realtime/v1",a),this.realtimeUrl.protocol=this.realtimeUrl.protocol.replace("http","ws"),this.authUrl=new URL("auth/v1",a),this.storageUrl=new URL("storage/v1",a),this.functionsUrl=new URL("functions/v1",a);const u=`sb-${a.hostname.split(".")[0]}-auth-token`,c={db:_y,realtime:ky,auth:Object.assign(Object.assign({},Sy),{storageKey:u}),global:wy},d=Oy(r??{},c);this.storageKey=(i=d.auth.storageKey)!==null&&i!==void 0?i:"",this.headers=(s=d.global.headers)!==null&&s!==void 0?s:{},d.accessToken?(this.accessToken=d.accessToken,this.auth=new Proxy({},{get:(f,g)=>{throw new Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(g)} is not possible`)}})):this.auth=this._initSupabaseAuthClient((o=d.auth)!==null&&o!==void 0?o:{},this.headers,d.global.fetch),this.fetch=Py(n,this._getAccessToken.bind(this),d.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},d.realtime)),this.rest=new Bm(new URL("rest/v1",a).href,{headers:this.headers,schema:d.db.schema,fetch:this.fetch}),d.accessToken||this._listenForAuthEvents()}get functions(){return new ym(this.functionsUrl.href,{headers:this.headers,customFetch:this.fetch})}get storage(){return new gy(this.storageUrl.href,this.headers,this.fetch)}from(t){return this.rest.from(t)}schema(t){return this.rest.schema(t)}rpc(t,n={},r={}){return this.rest.rpc(t,n,r)}channel(t,n={config:{}}){return this.realtime.channel(t,n)}getChannels(){return this.realtime.getChannels()}removeChannel(t){return this.realtime.removeChannel(t)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var t,n;return x0(this,void 0,void 0,function*(){if(this.accessToken)return yield this.accessToken();const{data:r}=yield this.auth.getSession();return(n=(t=r.session)===null||t===void 0?void 0:t.access_token)!==null&&n!==void 0?n:null})}_initSupabaseAuthClient({autoRefreshToken:t,persistSession:n,detectSessionInUrl:r,storage:i,storageKey:s,flowType:o,lock:l,debug:a},u,c){const d={Authorization:`Bearer ${this.supabaseKey}`,apikey:`${this.supabaseKey}`};return new E0({url:this.authUrl.href,headers:Object.assign(Object.assign({},d),u),storageKey:s,autoRefreshToken:t,persistSession:n,detectSessionInUrl:r,storage:i,flowType:o,lock:l,debug:a,fetch:c,hasCustomAuthorizationHeader:"Authorization"in this.headers})}_initRealtimeClient(t){return new ry(this.realtimeUrl.href,Object.assign(Object.assign({},t),{params:Object.assign({apikey:this.supabaseKey},t==null?void 0:t.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange((n,r)=>{this._handleTokenChanged(n,"CLIENT",r==null?void 0:r.access_token)})}_handleTokenChanged(t,n,r){(t==="TOKEN_REFRESHED"||t==="SIGNED_IN")&&this.changedAccessToken!==r?this.changedAccessToken=r:t==="SIGNED_OUT"&&(this.realtime.setAuth(),n=="STORAGE"&&this.auth.signOut(),this.changedAccessToken=void 0)}}const P0=(e,t,n)=>new C0(e,t,n),T0="https://jpvbtrzvbpyzgtpvltss.supabase.co",j0="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpwdmJ0cnp2YnB5emd0cHZsdHNzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA5MTM1MzAsImV4cCI6MjA2NjQ4OTUzMH0.TTEAu4XUOXRW-gBvs1qSlSx92fnW7apyMY_KTnQiUbI";P0(T0,j0);class O0{async signUp(t,n,r){try{return await(await fetch("/api/auth/signup",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:t,password:n,name:r})})).json()}catch{return{success:!1,error:"Network error during signup"}}}async signIn(t,n){try{const i=await(await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:t,password:n})})).json();return i.success&&i.token&&localStorage.setItem("auth_token",i.token),i}catch{return{success:!1,error:"Network error during login"}}}async signOut(){try{const t=localStorage.getItem("auth_token");t&&await fetch("/api/auth/logout",{method:"POST",headers:{Authorization:`Bearer ${t}`}})}finally{localStorage.removeItem("auth_token")}}async getCurrentUser(){try{const t=localStorage.getItem("auth_token");if(!t)return null;const n=await fetch("/api/auth/user",{headers:{Authorization:`Bearer ${t}`}});if(!n.ok)return n.status===401&&localStorage.removeItem("auth_token"),null;const r=await n.json();return r.success?r.data:null}catch{return null}}getToken(){return localStorage.getItem("auth_token")}isAuthenticated(){return!!this.getToken()}}const _i=new O0,Ca=cm((e,t)=>({user:null,isLoading:!1,isAuthenticated:!1,login:async(n,r)=>{e({isLoading:!0});try{const i=await _i.signIn(n,r);return i.success&&i.user?(e({user:i.user,isAuthenticated:!0,isLoading:!1}),{success:!0}):(e({isLoading:!1}),{success:!1,error:i.error||"Login failed"})}catch{return e({isLoading:!1}),{success:!1,error:"Network error"}}},signup:async(n,r,i)=>{e({isLoading:!0});try{const s=await _i.signUp(n,r,i);return s.success&&s.user?(e({user:s.user,isAuthenticated:!0,isLoading:!1}),{success:!0}):(e({isLoading:!1}),{success:!1,error:s.error||"Signup failed"})}catch{return e({isLoading:!1}),{success:!1,error:"Network error"}}},logout:async()=>{e({isLoading:!0});try{await _i.signOut()}finally{e({user:null,isAuthenticated:!1,isLoading:!1})}},checkAuth:async()=>{e({isLoading:!0});try{const n=await _i.getCurrentUser();e({user:n,isAuthenticated:!!n,isLoading:!1})}catch{e({user:null,isAuthenticated:!1,isLoading:!1})}},updateUser:n=>{const{user:r}=t();r&&e({user:{...r,...n}})}})),ah=({children:e,onClick:t,variant:n="primary",size:r="md",isLoading:i=!1,disabled:s=!1,type:o="button",className:l="",...a})=>{const u="font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",c={primary:"bg-primary-500 hover:bg-primary-600 text-white focus:ring-primary-500",secondary:"border-2 border-primary-500 text-primary-500 hover:bg-primary-50 focus:ring-primary-500",danger:"bg-red-500 hover:bg-red-600 text-white focus:ring-red-500"},d={sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-base",lg:"px-6 py-3 text-lg"},f=`${u} ${c[n]} ${d[r]} ${l}`;return j.jsx("button",{type:o,onClick:t,disabled:s||i,className:f,...a,children:i?j.jsxs("div",{className:"flex items-center",children:[j.jsxs("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-current",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[j.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),j.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Loading..."]}):e})},kn=({label:e,placeholder:t,value:n,onChange:r,type:i="text",error:s,required:o=!1,disabled:l=!1,className:a="",...u})=>{const c=`w-full px-3 py-2 border-2 rounded-md bg-background-secondary text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed ${s?"border-red-500":"border-gray-600 focus:border-primary-500"} ${a}`;return j.jsxs("div",{className:"w-full",children:[e&&j.jsxs("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:[e,o&&j.jsx("span",{className:"text-red-500 ml-1",children:"*"})]}),j.jsx("input",{type:i,value:n,onChange:d=>r(d.target.value),placeholder:t,disabled:l,className:c,...u}),s&&j.jsx("p",{className:"mt-1 text-sm text-red-500",children:s})]})},R0=()=>{const[e,t]=x.useState(""),[n,r]=x.useState(""),[i,s]=x.useState({}),{login:o,isLoading:l}=Ca(),a=xs(),u=()=>{const d={};return e?/\S+@\S+\.\S+/.test(e)||(d.email="Email is invalid"):d.email="Email is required",n||(d.password="Password is required"),s(d),Object.keys(d).length===0},c=async d=>{if(d.preventDefault(),!u())return;const f=await o(e,n);f.success?a("/dashboard"):s({general:f.error})};return j.jsx("div",{className:"min-h-screen flex items-center justify-center bg-background-primary",children:j.jsxs("div",{className:"max-w-md w-full space-y-8 p-8",children:[j.jsxs("div",{children:[j.jsx("h2",{className:"mt-6 text-center text-3xl font-extrabold text-white",children:"Sign in to ChewyAI"}),j.jsxs("p",{className:"mt-2 text-center text-sm text-gray-400",children:["Or"," ",j.jsx(Lf,{to:"/signup",className:"font-medium text-primary-500 hover:text-primary-400",children:"create a new account"})]})]}),j.jsxs("form",{className:"mt-8 space-y-6",onSubmit:c,children:[i.general&&j.jsx("div",{className:"bg-red-900 border border-red-700 text-red-100 px-4 py-3 rounded",children:i.general}),j.jsxs("div",{className:"space-y-4",children:[j.jsx(kn,{label:"Email address",type:"email",value:e,onChange:t,error:i.email,placeholder:"Enter your email",required:!0}),j.jsx(kn,{label:"Password",type:"password",value:n,onChange:r,error:i.password,placeholder:"Enter your password",required:!0})]}),j.jsx(ah,{type:"submit",isLoading:l,className:"w-full",size:"lg",children:"Sign in"})]})]})})},I0=()=>{const[e,t]=x.useState({name:"",email:"",password:"",confirmPassword:""}),[n,r]=x.useState({}),{signup:i,isLoading:s}=Ca(),o=xs(),l=()=>{const c={};return e.email?/\S+@\S+\.\S+/.test(e.email)||(c.email="Email is invalid"):c.email="Email is required",e.password?e.password.length<6&&(c.password="Password must be at least 6 characters"):c.password="Password is required",e.password!==e.confirmPassword&&(c.confirmPassword="Passwords do not match"),r(c),Object.keys(c).length===0},a=async c=>{if(c.preventDefault(),!l())return;const d=await i(e.email,e.password,e.name||void 0);d.success?o("/dashboard"):r({general:d.error||"Signup failed"})},u=(c,d)=>t(f=>({...f,[c]:d}));return j.jsx("div",{className:"min-h-screen flex items-center justify-center bg-background-primary",children:j.jsxs("div",{className:"max-w-md w-full space-y-8 p-8",children:[j.jsxs("div",{children:[j.jsx("h2",{className:"mt-6 text-center text-3xl font-extrabold text-white",children:"Create your account"}),j.jsxs("p",{className:"mt-2 text-center text-sm text-gray-400",children:["Or"," ",j.jsx(Lf,{to:"/login",className:"font-medium text-primary-500 hover:text-primary-400",children:"sign in to your existing account"})]})]}),j.jsxs("form",{className:"mt-8 space-y-6",onSubmit:a,children:[n.general&&j.jsx("div",{className:"bg-red-900 border border-red-700 text-red-100 px-4 py-3 rounded",children:n.general}),j.jsxs("div",{className:"space-y-4",children:[j.jsx(kn,{label:"Full Name (Optional)",value:e.name,onChange:c=>u("name",c),placeholder:"Enter your full name"}),j.jsx(kn,{label:"Email address",type:"email",value:e.email,onChange:c=>u("email",c),error:n.email,placeholder:"Enter your email",required:!0}),j.jsx(kn,{label:"Password",type:"password",value:e.password,onChange:c=>u("password",c),error:n.password,placeholder:"Create a password",required:!0}),j.jsx(kn,{label:"Confirm Password",type:"password",value:e.confirmPassword,onChange:c=>u("confirmPassword",c),error:n.confirmPassword,placeholder:"Confirm your password",required:!0})]}),j.jsx(ah,{type:"submit",isLoading:s,className:"w-full",size:"lg",children:"Create Account"})]})]})})},$0=({children:e})=>{const{isAuthenticated:t,isLoading:n,checkAuth:r}=Ca(),i=Wn();return x.useEffect(()=>{r()},[r]),n?j.jsx("div",{className:"min-h-screen flex items-center justify-center bg-background-primary",children:j.jsx("div",{className:"text-white",children:"Loading..."})}):t?j.jsx(j.Fragment,{children:e}):j.jsx($f,{to:"/login",state:{from:i},replace:!0})},L0=()=>j.jsx("div",{className:"min-h-screen bg-background-primary text-white flex items-center justify-center",children:j.jsx("h1",{className:"text-3xl font-bold",children:"Welcome to ChewyAI Dashboard"})});function A0(){return j.jsx(Ng,{children:j.jsxs(jg,{children:[j.jsx(lr,{path:"/",element:j.jsx($f,{to:"/login"})}),j.jsx(lr,{path:"/login",element:j.jsx(R0,{})}),j.jsx(lr,{path:"/signup",element:j.jsx(I0,{})}),j.jsx(lr,{path:"/dashboard",element:j.jsx($0,{children:j.jsx(L0,{})})})]})})}mo.createRoot(document.getElementById("root")).render(j.jsx(Cl.StrictMode,{children:j.jsx(A0,{})}));
