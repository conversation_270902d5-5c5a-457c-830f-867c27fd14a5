"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.requireCredits = void 0;
const creditService_1 = require("../services/creditService");
// Middleware to check if user has sufficient credits for an operation
const requireCredits = (operationType) => {
    return async (req, res, next) => {
        try {
            const userId = req.user.id;
            const hasCredits = await creditService_1.creditService.validateCreditOperation(userId, operationType);
            if (!hasCredits) {
                const creditCheck = await creditService_1.creditService.checkSufficientCredits(userId, operationType);
                return res.status(402).json({
                    success: false,
                    error: 'Insufficient credits',
                    data: {
                        currentCredits: creditCheck.currentCredits,
                        requiredCredits: creditCheck.requiredCredits,
                        shortfall: creditCheck.shortfall
                    }
                });
            }
            // Store operation type in request for later use
            req.creditOperation = operationType;
            next();
        }
        catch (error) {
            console.error('Credit check error:', error);
            res.status(500).json({
                success: false,
                error: 'Credit validation failed'
            });
        }
    };
};
exports.requireCredits = requireCredits;
//# sourceMappingURL=creditCheck.js.map