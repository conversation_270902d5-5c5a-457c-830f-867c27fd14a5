"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const compression_1 = __importDefault(require("compression"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const path_1 = __importDefault(require("path"));
const dotenv_1 = __importDefault(require("dotenv"));
const supabase_1 = __importDefault(require("./config/supabase"));
const auth_1 = __importDefault(require("./routes/auth"));
const documents_1 = __importDefault(require("./routes/documents"));
const credits_1 = __importDefault(require("./routes/credits"));
// Load environment variables
dotenv_1.default.config();
const app = (0, express_1.default)();
const PORT = process.env.PORT || 3001;
// Security middleware
app.use((0, helmet_1.default)({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
            fontSrc: ["'self'", "https://fonts.gstatic.com"],
            imgSrc: ["'self'", "data:", "https:"],
            scriptSrc: ["'self'"],
        },
    },
}));
// Rate limiting
const limiter = (0, express_rate_limit_1.default)({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // limit each IP to 100 requests per windowMs
    message: "Too many requests from this IP, please try again later.",
});
app.use("/api", limiter);
// CORS configuration
app.use((0, cors_1.default)({
    origin: process.env.NODE_ENV === "production"
        ? process.env.FRONTEND_URL
        : ["http://localhost:3000", "http://127.0.0.1:3000"],
    credentials: true,
}));
// Body parsing middleware
app.use((0, compression_1.default)());
app.use(express_1.default.json({ limit: "10mb" }));
app.use(express_1.default.urlencoded({ extended: true, limit: "10mb" }));
// Health check endpoint
app.get("/api/health", async (_req, res) => {
    try {
        // Test database connection
        const { data, error } = await supabase_1.default
            .from("ai_operation_costs")
            .select("count")
            .limit(1);
        const dbStatus = error ? "error" : "connected";
        const dbRecords = data ? data.length : 0;
        res.json({
            status: "ok",
            timestamp: new Date().toISOString(),
            version: "1.0.0",
            environment: process.env.NODE_ENV || "development",
            database: dbStatus,
            dbRecords,
        });
    }
    catch (error) {
        res.status(500).json({
            status: "error",
            timestamp: new Date().toISOString(),
            version: "1.0.0",
            environment: process.env.NODE_ENV || "development",
            database: "error",
            error: "Database connection failed",
        });
    }
});
// API routes
app.use("/api/auth", auth_1.default);
app.use("/api/documents", documents_1.default);
app.use("/api/credits", credits_1.default);
app.get("/api", (_req, res) => {
    res.json({
        message: "ChewyAI API Server",
        version: "1.0.0",
        endpoints: {
            health: "/api/health",
            auth: "/api/auth",
            documents: "/api/documents",
            credits: "/api/credits",
            // AI generation endpoints will be added in Phase 7
        },
    });
});
// Serve static files from frontend build (production)
if (process.env.NODE_ENV === "production") {
    app.use(express_1.default.static(path_1.default.join(__dirname, "../public")));
    // Catch all handler: send back React's index.html file for SPA routing
    app.get("*", (_req, res) => {
        res.sendFile(path_1.default.join(__dirname, "../public/index.html"));
    });
}
// Error handling middleware
app.use((err, _req, res, _next) => {
    console.error("Error:", err);
    const status = err.status || 500;
    const message = process.env.NODE_ENV === "production"
        ? "Internal server error"
        : err.message;
    res.status(status).json({
        success: false,
        error: message,
        ...(process.env.NODE_ENV === "development" && { stack: err.stack }),
    });
});
// 404 handler for API routes
app.use("/api/*", (req, res) => {
    res.status(404).json({
        success: false,
        error: "API endpoint not found",
        path: req.path,
    });
});
app.listen(PORT, () => {
    console.log(`🚀 ChewyAI Backend Server running on port ${PORT}`);
    console.log(`📊 Environment: ${process.env.NODE_ENV || "development"}`);
    console.log(`🔗 Health check: http://localhost:${PORT}/api/health`);
});
//# sourceMappingURL=index.js.map