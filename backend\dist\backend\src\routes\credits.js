"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_1 = require("../middleware/auth");
const creditCheck_1 = require("../middleware/creditCheck");
const creditController_1 = require("../controllers/creditController");
const router = (0, express_1.Router)();
// All credit routes require authentication
router.use(auth_1.authenticateToken);
// Credit information routes
router.get('/balance', creditController_1.getCreditBalance);
router.get('/history', creditController_1.getCreditHistory);
router.get('/stats', creditController_1.getCreditStats);
router.get('/pricing', creditController_1.getOperationCosts);
router.get('/check/:operationType', creditController_1.checkCreditSufficiency);
// Test endpoint to demonstrate credit middleware
router.post('/test-deduction/:operationType', (0, creditCheck_1.requireCredits)('flashcard_generation'), async (req, res) => {
    try {
        const { operationType } = req.params;
        const userId = req.user.id;
        // Import creditService here to avoid circular dependency
        const { creditService } = await Promise.resolve().then(() => __importStar(require('../services/creditService')));
        // Deduct credits for the operation
        const result = await creditService.deductCredits(userId, operationType, { test: true, endpoint: 'test-deduction' });
        res.json({
            success: true,
            message: 'Credits deducted successfully',
            data: result
        });
    }
    catch (error) {
        console.error('Test deduction error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to deduct credits'
        });
    }
});
exports.default = router;
//# sourceMappingURL=credits.js.map