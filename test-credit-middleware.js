const fetch = require('node-fetch');

async function testCreditMiddleware() {
  try {
    // First, login to get a token
    console.log('🔐 Logging in...');
    const loginResponse = await fetch('http://localhost:3001/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'testpass123'
      })
    });

    if (!loginResponse.ok) {
      throw new Error(`Login failed: ${loginResponse.status}`);
    }

    const loginData = await loginResponse.json();
    const token = loginData.token;
    console.log('✅ Login successful');
    console.log('Initial credits:', loginData.user.credits_remaining);

    // Test 1: Test credit middleware with sufficient credits
    console.log('\n💰 Testing credit middleware with sufficient credits...');
    const testResponse1 = await fetch('http://localhost:3001/api/credits/test-deduction/flashcard_generation', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (!testResponse1.ok) {
      const errorText = await testResponse1.text();
      throw new Error(`Test deduction failed: ${testResponse1.status} - ${errorText}`);
    }

    const testData1 = await testResponse1.json();
    console.log('✅ Credit deduction successful:', testData1);

    // Check remaining credits
    const balanceResponse = await fetch('http://localhost:3001/api/credits/balance', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    const balanceData = await balanceResponse.json();
    console.log('Remaining credits after deduction:', balanceData.data.credits);

    // Test 2: Try to exhaust credits to test insufficient credits scenario
    console.log('\n🔄 Testing multiple deductions to test insufficient credits...');
    let attempts = 0;
    let lastResponse;
    
    while (attempts < 15) { // Try up to 15 times to exhaust credits
      attempts++;
      console.log(`Attempt ${attempts}...`);
      
      const testResponse = await fetch('http://localhost:3001/api/credits/test-deduction/flashcard_generation', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      lastResponse = testResponse;
      
      if (testResponse.status === 402) {
        // Insufficient credits - this is what we want to test
        const errorData = await testResponse.json();
        console.log('✅ Credit middleware correctly blocked request with insufficient credits:');
        console.log(errorData);
        break;
      } else if (testResponse.ok) {
        const successData = await testResponse.json();
        console.log(`  ✓ Deduction ${attempts} successful, remaining: ${successData.data.remainingCredits}`);
      } else {
        const errorText = await testResponse.text();
        console.log(`  ❌ Unexpected error: ${testResponse.status} - ${errorText}`);
        break;
      }
    }

    if (lastResponse && lastResponse.status !== 402) {
      console.log('⚠️  Did not reach insufficient credits scenario after 15 attempts');
    }

    console.log('\n🎉 Credit middleware tests completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  }
}

testCreditMiddleware();
